"use client";

import { CalendarIcon } from "lucide-react";
import { Button } from "@/common/components/ui/button";
import { format } from "date-fns";
import { cn } from "@/common/lib/shadcn-utils";
import { Badge } from "@/common/components/ui/badge";
import { calculateRentalDays } from "@/common/lib/date-utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/common/components/ui/popover";
import { DateRange } from "react-day-picker";
import { Calendar } from "@/common/components/ui/calendar";
import { useIsMobile } from "@/common/hooks/use-mobile";

interface DateSelectorProps {
  mode: "pickup" | "dropoff";
  label: string;
  dateRange: DateRange | undefined;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
  onPopoverOpenChange?: (open: boolean) => void;
  disabledDays?: (date: Date) => boolean;
  className?: string;
  pickupTime?: string;
  dropoffTime?: string;
}

export function DateSelector({
  mode,
  label,
  dateRange,
  onDateRangeChange,
  onPopoverOpenChange,
  disabledDays,
  className,
  pickupTime = "08:00",
  dropoffTime = "08:00",
}: DateSelectorProps) {
  const isMobile = useIsMobile();

  const handleDayClick = (day: Date) => {
    if (mode === "pickup") {
      // Pick-up date logic
      if (dateRange && dateRange.from && dateRange.to) {
        onDateRangeChange({ from: day, to: undefined });
        return;
      }

      if (dateRange?.from && !dateRange.to) {
        if (day <= dateRange.from) {
          onDateRangeChange({ from: day, to: undefined });
        } else {
          onDateRangeChange({ from: dateRange.from, to: day });
        }
      } else {
        onDateRangeChange({ from: day, to: undefined });
      }
    } else {
      // Drop-off date logic
      if (dateRange?.from) {
        if (
          day.getFullYear() === dateRange.from.getFullYear() &&
          day.getMonth() === dateRange.from.getMonth() &&
          day.getDate() === dateRange.from.getDate()
        ) {
          return;
        }
        if (day > dateRange.from) {
          onDateRangeChange({ from: dateRange.from, to: day });
        } else {
          onDateRangeChange({ from: day, to: undefined });
        }
      } else {
        onDateRangeChange({ from: day, to: undefined });
      }
    }
  };

  const displayDate =
    mode === "pickup" ? dateRange?.from : dateRange?.to || dateRange?.from;

  return (
    <div className={className}>
      <Popover onOpenChange={onPopoverOpenChange}>
        <PopoverTrigger asChild>
          <Button
            className={cn(
              "bg-background text-foreground hover:bg-accent border-input h-12 w-full justify-start border text-left font-normal",
            )}
          >
            <div className="flex flex-col">
              <span className="text-xs">{label}</span>
              {displayDate ? (
                format(displayDate, "EEE dd MMM")
              ) : (
                <div className="flex items-center gap-2">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  <span>Select date</span>
                </div>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <div className="border-b p-3">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">
                {mode === "pickup"
                  ? "Select pick-up date"
                  : "Select drop-off date"}
              </p>
              {dateRange?.from && dateRange?.to && (
                <Badge className="ml-2">
                  {(() => {
                    const days = calculateRentalDays(
                      dateRange.from,
                      dateRange.to,
                      pickupTime,
                      dropoffTime,
                    );
                    return `${days} ${days === 1 ? "day" : "days"}`;
                  })()}
                </Badge>
              )}
            </div>
            <p className="mt-0.5 text-xs text-gray-500">
              Click a date to select a range
            </p>
          </div>
          <Calendar
            mode="range"
            showOutsideDays={false}
            defaultMonth={dateRange?.from || new Date()}
            selected={dateRange}
            onDayClick={handleDayClick}
            numberOfMonths={isMobile ? 1 : 2}
            disabled={disabledDays}
            className="rounded-md"
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
