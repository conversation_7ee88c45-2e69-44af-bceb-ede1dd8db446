package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.GeneralResponse;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateResultDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateUpdateDto;
import com.cap10mycap10.worklinkservice.permissions.assignmentcoderate.CreateAssignmentCodeRate;
import com.cap10mycap10.worklinkservice.permissions.assignmentcoderate.DeleteAssignmentCodeRate;
import com.cap10mycap10.worklinkservice.permissions.assignmentcoderate.UpdateAssignmentCodeRate;
import com.cap10mycap10.worklinkservice.permissions.assignmentcoderate.ViewAssignmentCodeRate;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.validation.Valid;
import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class AssignmentCodeRateController {

    private final AssignmentCodeRateService assignmentCodeRateService;

    public AssignmentCodeRateController(AssignmentCodeRateService assignmentCodeRateService) {
        this.assignmentCodeRateService = assignmentCodeRateService;
    }

    /*@CreateAssignmentCodeRate*/
    @PostMapping(value = "assignment-code-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AssignmentCodeRateResultDto> create(@RequestBody AssignmentCodeRateCreateDto assignmentCodeCreateDto) {
        log.info("Request to add assignment code rate: {}", assignmentCodeCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(assignmentCodeRateService.save(assignmentCodeCreateDto));
    }


    @PostMapping(value = "assignment-code-rate-multiple", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralResponse> create(@RequestBody List<AssignmentCodeRateCreateDto> assignmentCodeCreateDto) {
        log.info("Request to add assignment code rate: {}", assignmentCodeCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        assignmentCodeRateService.saveAll(assignmentCodeCreateDto);
        return ResponseEntity.created(uri)
                .body(new GeneralResponse("Assignment code rates saved successfully", "00"));
    }

   /* @ViewAssignmentCodeRate*/
    @GetMapping(value = "assignment-code-rate/{id}")
    public ResponseEntity<AssignmentCodeRateResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get assignment code rate with id : {}", id);
        return ResponseEntity.ok(assignmentCodeRateService.findById(id));
    }


   /* @ViewAssignmentCodeRate*/
    @GetMapping(value = "assignment-code-rates-agency/{payeeId}")
    public ResponseEntity<List<AssignmentCodeRateResultDto>> findByByAgentId(@PathVariable("payeeId") Long agentId,
                                                                             @RequestParam(value = "day", required = false) String day) {
        log.info("Request to get all assignment code rates");
        if (day==null){
        return ResponseEntity.ok(assignmentCodeRateService.findAllByAgentId(agentId));
        }else{
            return ResponseEntity.ok(assignmentCodeRateService.findAllByAgentId(agentId, day));
        }
    }

    @GetMapping(value = "assignment-code-rates-client/{payerId}")
    public ResponseEntity<List<AssignmentCodeRateResultDto>> findByByClientId(@PathVariable("payerId") Long clientId,
                                                                             @RequestParam(value = "day", required = false) String day) {
        log.info("Request to get all assignment code rates");
//        if (day==null){
        return ResponseEntity.ok(assignmentCodeRateService.findAllByClientId(clientId));
//        }else{
//            return ResponseEntity.ok(assignmentCodeRateService.findAllByAgentId(payerId, day));
//        }
    }

    /*@ViewAssignmentCodeRate*/
    @GetMapping(value = "assignment-code-rates")
    public ResponseEntity<List<AssignmentCodeRateResultDto>> findById() {
        log.info("Request to get all assignment code rates");
        return ResponseEntity.ok(assignmentCodeRateService.findAll());
    }

    /*@ViewAssignmentCodeRate*/
    @GetMapping(value = "assignment-code-rates/{page}/{size}")
    public ResponseEntity<Page<AssignmentCodeRateResultDto>> findById(@PathVariable("page") int page,
                                                                      @PathVariable("size") int size) {
        log.info("Request to get assignment code rate: {}, {}", page, size);
        return ResponseEntity.ok(assignmentCodeRateService.findAllPaged(PageRequest.of(page, size)));
    }

    /*@UpdateAssignmentCodeRate*/
    @PutMapping(value = "assignment-code-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AssignmentCodeRateResultDto> update(@RequestBody @Valid AssignmentCodeRateUpdateDto assignmentCodeUpdateDto) {
        log.info("Request to update assignment code rate: {}", assignmentCodeUpdateDto);
        return ResponseEntity.ok(assignmentCodeRateService.save(assignmentCodeUpdateDto));
    }

   /* @DeleteAssignmentCodeRate*/
    @DeleteMapping(value = "assignment-code-rate/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete assignment code rate with id : {}", id);
        assignmentCodeRateService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
