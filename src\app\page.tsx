import Header from "@/app/components/header";
// import CarBrands from "@/app/components/vehicle-brands";
import WhyChooseUs from "@/app/components/why-choose-us";
import PopularLocations from "@/app/components/popular-locations";
import Testimonials from "@/app/components/testimonials";
import type { Metadata } from "next";
import { getCurrentBrandConfig } from '@/common/config/brands/utils';

export async function generateMetadata(): Promise<Metadata> {
  const brandConfig = getCurrentBrandConfig();

  // Only generate SEO metadata for Karlink brand
  if (brandConfig.name.toLowerCase() !== 'karlink') {
    return {
      title: `${brandConfig.brandName} - Car Rental Services`,
      description: `Car rental services by ${brandConfig.brandName}`,
      robots: 'noindex, nofollow',
    };
  }

  return {
    title: "Car Rental & Vehicle Sharing Platform in Zimbabwe",
    description:
      "Discover affordable car rental and vehicle sharing in Zimbabwe with MyKarLink. Rent from trusted local hosts, enjoy comprehensive insurance, and explore Zimbabwe with confidence. Book your perfect car today.",
    keywords: [
      "car rental Zimbabwe",
      "vehicle sharing Zimbabwe",
      "car hire Zimbabwe",
      "Harare car rental",
      "Bulawayo car rental",
      "peer-to-peer car sharing",
      "affordable car rental",
      "car booking Zimbabwe",
      "vehicle rental platform",
      "trusted car hosts Zimbabwe",
      "Zimbabwe transport",
      "car sharing platform",
    ],

    alternates: {
      canonical: "https://mykarlink.com",
    },

    openGraph: {
      title: "MyKarLink - Car Rental & Vehicle Sharing in Zimbabwe",
      description:
        "Discover affordable car rental and vehicle sharing in Zimbabwe. Rent from trusted local hosts with comprehensive insurance coverage. Book your perfect car today.",
      url: "https://mykarlink.com",
      type: "website",
      images: [
        {
          url: "https://mykarlink.com/brands/karlink/images/og-image.png",
          width: 1200,
          height: 630,
          alt: "MyKarLink Car Rental Platform - Browse and rent cars from trusted hosts",
        },
      ],
    },

    twitter: {
      card: "summary_large_image",
      title: "MyKarLink - Car Rental & Vehicle Sharing in Zimbabwe",
      description:
        "Discover affordable car rental and vehicle sharing in Zimbabwe. Rent from trusted local hosts.",
      images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
    },
  };
}

function Home() {
  const brandConfig = getCurrentBrandConfig();
  const isKarlink = brandConfig.name.toLowerCase() === 'karlink';

  // Only generate structured data for Karlink brand
  const jsonLd = isKarlink ? {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "MyKarLink",
    alternateName: "MyKarLink Zimbabwe",
    url: "https://mykarlink.com",
    description:
      "Car rental and vehicle sharing platform connecting car owners with renters in Zimbabwe",
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate:
          "https://mykarlink.com/vehicles?search={search_term_string}",
      },
      "query-input": "required name=search_term_string",
    },
    sameAs: [
      "https://www.facebook.com/people/My-Karlink/61572568015730/",
      "https://www.instagram.com/my_karlink/",
      "https://x.com/My_KarLink",
      "https://www.youtube.com/@mykarlink",
      "https://www.tiktok.com/@mykarlink",
    ],
  } : null;

  const organizationJsonLd = isKarlink ? {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "MyKarLink",
    legalName: "MyKarLink PVT LTD",
    url: "https://mykarlink.com",
    logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
    description: "Leading car rental and vehicle sharing platform in Zimbabwe",
    foundingDate: "2024",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+263779144386",
      contactType: "customer service",
      email: "<EMAIL>",
      availableLanguage: ["English"],
      areaServed: "ZW",
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: "7 St Antony, Cnr Five Av and Fifth St",
      addressLocality: "Harare",
      addressCountry: "ZW",
    },
    sameAs: [
      "https://www.facebook.com/people/My-Karlink/61572568015730/",
      "https://www.instagram.com/my_karlink/",
      "https://x.com/My_KarLink",
      "https://www.youtube.com/@mykarlink",
      "https://www.tiktok.com/@mykarlink",
    ],
    serviceType: "Car Rental and Vehicle Sharing",
  } : null;

  const serviceJsonLd = isKarlink ? {
    "@context": "https://schema.org",
    "@type": "Service",
    name: "Car Rental and Vehicle Sharing",
    description:
      "Peer-to-peer car rental and vehicle sharing services connecting car owners with renters",
    provider: {
      "@type": "Organization",
      name: "MyKarLink",
    },
    areaServed: {
      "@type": "Country",
      name: "Zimbabwe",
    },
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Vehicle Rental Services",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Economy Car Rental",
            description: "Affordable economy cars for daily transportation",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "SUV Rental",
            description:
              "Spacious SUVs perfect for family trips and adventures",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "Luxury Car Rental",
            description: "Premium vehicles for special occasions",
          },
        },
      ],
    },
  } : null;

  return (
    <>
      {/* Structured Data - Only for Karlink */}
      {isKarlink && jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd).replace(/</g, "\\u003c"),
          }}
        />
      )}
      {isKarlink && organizationJsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationJsonLd).replace(/</g, "\\u003c"),
          }}
        />
      )}
      {isKarlink && serviceJsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(serviceJsonLd).replace(/</g, "\\u003c"),
          }}
        />
      )}

      <Header />
      {/* <CarBrands /> */}
      <WhyChooseUs />
      <PopularLocations />
      <Testimonials />
    </>
  );
}

export default Home;
