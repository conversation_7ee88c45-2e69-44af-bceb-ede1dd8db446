package com.cap10mycap10.worklinkservice.config;

import com.cap10mycap10.worklinkservice.search.AgencySearchService;
import com.cap10mycap10.worklinkservice.search.ClientSearchService;
import com.cap10mycap10.worklinkservice.search.ShiftSearchService;
import com.cap10mycap10.worklinkservice.search.WorkerSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;

@Configuration
public class HibernateSearchConfiguration {


    private final EntityManager entityManager;


    @Autowired
    public HibernateSearchConfiguration(EntityManagerFactory entityManagerFactory) {
        this.entityManager = entityManagerFactory.createEntityManager();
    }

    @Bean
    WorkerSearchService hibernateSearchService() {
        WorkerSearchService policyHolderSearchService = new WorkerSearchService(entityManager.getEntityManagerFactory());
        policyHolderSearchService.initializeHibernateSearch();
        return policyHolderSearchService;
    }

    @Bean
    ClientSearchService hibernateAccidentSearchService() {
        ClientSearchService policyAccidentSearchService = new ClientSearchService(entityManager.getEntityManagerFactory());
        policyAccidentSearchService.initializeHibernateSearch();
        return policyAccidentSearchService;
    }

    @Bean
    ShiftSearchService hibernateFuneralSearchService() {
        ShiftSearchService policyFuneralSearchService = new ShiftSearchService(entityManager.getEntityManagerFactory());
        policyFuneralSearchService.initializeHibernateSearch();
        return policyFuneralSearchService;
    }

    @Bean
    AgencySearchService hibernateSavingsSearchService() {
        AgencySearchService policySavingsSearchService = new AgencySearchService(entityManager.getEntityManagerFactory());
        policySavingsSearchService.initializeHibernateSearch();
        return policySavingsSearchService;
    }


}
