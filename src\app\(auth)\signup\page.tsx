"use client";

import Link from "next/link";
import { useState, useActionState, startTransition } from "react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { signupAction, FormState, SignupFormData } from "@/app/(auth)/actions";
import { Checkbox } from "@/common/components/ui/checkbox";
import Loader from "@/common/components/loader";
import { isKarlinkBrand } from "@/common/config/brands/utils";
import { PhoneInput } from "@/common/components/ui/phone-input";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/common/lib/shadcn-utils";
import { Car, Key } from "lucide-react";

type UserType = "AGENCY" | "CLIENT" | null;

const getFormData = (state: FormState): SignupFormData | undefined => {
  if (!state) return undefined;
  if ("formData" in state) {
    return state.formData as SignupFormData;
  }
  return undefined;
};

// Create dynamic schema based on user type
const createUserSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required")
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  telephone: z.string().min(1, "Phone number is required"),
  companyName: z
    .string()
    .optional()
    .refine((val) => val === undefined || val.length >= 2, {
      message: "Company name must be at least 2 characters",
    }),

  terms: z
    .boolean()
    .refine(
      (value) => value === true,
      "You must accept the terms and conditions",
    ),
});

type ClientFormData = z.infer<typeof createUserSchema>;

function SignupForm({
  userType,
  onBack,
}: {
  userType: NonNullable<UserType>;
  onBack: () => void;
}) {
  const [state, action, isPending] = useActionState(signupAction, undefined);
  const formData = getFormData(state);
  const router = useRouter();

  // Phone validation state
  const [phoneValidation, setPhoneValidation] = useState({
    isValid: false,
    error: null as string | null,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
    setError,
  } = useForm<ClientFormData>({
    resolver: zodResolver(createUserSchema),
    mode: "onChange",
    defaultValues: {
      firstName: formData?.firstName || "",
      lastName: formData?.lastName || "",
      email: formData?.email || "",
      telephone: formData?.telephone || "",
      ...(userType === "AGENCY" && {
        companyName: formData?.companyName || "",
      }),
      terms: false,
    },
  });

  useEffect(() => {
    if (
      state &&
      "success" in state &&
      state.success === true &&
      "redirect" in state
    ) {
      if ("message" in state && state.message) {
        sessionStorage.setItem("auth_message", state.message);
      }
      router.push(state.redirect);
    }
  }, [state, router]);

  const onSubmit = (data: ClientFormData) => {
    // Check phone validation before submitting
    if (!phoneValidation.isValid && data.telephone) {
      setError("telephone", {
        type: "manual",
        message: phoneValidation.error || "Please enter a valid phone number",
      });
      return;
    }

    // Create FormData for the action
    const formDataToSubmit = new FormData();
    formDataToSubmit.append("userType", userType);
    formDataToSubmit.append("firstName", data.firstName);
    formDataToSubmit.append("lastName", data.lastName);
    formDataToSubmit.append("email", data.email);
    formDataToSubmit.append("telephone", data.telephone);
    if (userType === "AGENCY" && data.companyName) {
      formDataToSubmit.append("companyName", data.companyName);
    }

    formDataToSubmit.append("terms", data.terms.toString());

    startTransition(() => {
      action(formDataToSubmit);
    });
  };

  const showBackButton = isKarlinkBrand();

  return (
    <div className="mx-auto w-full max-w-sm space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">
          Sign Up As{" "}
          {userType === "AGENCY" ? "Vehicle Provider" : "Vehicle Hirer"}
        </h1>
        <p className="text-gray-500">Enter your details below</p>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div>
          <div className="grid gap-4 py-4">
            {state &&
              "message" in state &&
              (!("success" in state) || !state.success) && (
                <p className="text-center text-sm text-red-500">
                  {state.message}
                </p>
              )}

            {state &&
              "violations" in state &&
              state.violations?.map(({ field, message }) => (
                <p key={field + message} className="text-destructive text-sm">
                  {message}
                </p>
              ))}

            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="firstName"
                    type="text"
                    className={cn(
                      errors.firstName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.firstName && (
                <p className="text-destructive text-sm">
                  {errors.firstName.message}
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="lastName"
                    type="text"
                    className={cn(
                      errors.lastName &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.lastName && (
                <p className="text-destructive text-sm">
                  {errors.lastName.message}
                </p>
              )}
            </div>

            {userType === "AGENCY" && (
              <div className="grid gap-2">
                <Label htmlFor="companyName">Company or trade name</Label>
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="companyName"
                      type="text"
                      className={cn(
                        errors.companyName &&
                          "border-destructive focus-visible:ring-destructive",
                      )}
                    />
                  )}
                />
                {errors.companyName && (
                  <p className="text-destructive text-sm">
                    {errors.companyName.message}
                  </p>
                )}
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="email"
                    type="email"
                    className={cn(
                      errors.email &&
                        "border-destructive focus-visible:ring-destructive",
                    )}
                  />
                )}
              />
              {errors.email && (
                <p className="text-destructive text-sm">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="telephone">Phone Number</Label>
              <Controller
                name="telephone"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <PhoneInput
                    value={value}
                    onChange={(phoneValue, isValid) => {
                      onChange(phoneValue);
                      setPhoneValidation({
                        isValid,
                        error: isValid
                          ? null
                          : "Please enter a valid phone number",
                      });
                    }}
                    onValidationChange={(isValid, error) => {
                      setPhoneValidation({
                        isValid,
                        error: error || null,
                      });
                    }}
                    placeholder="Enter your phone number"
                    error={!!errors.telephone}
                  />
                )}
              />
              {/* Show Zod validation errors */}
              {errors.telephone && (
                <p className="text-destructive text-sm">
                  {errors.telephone.message}
                </p>
              )}

              {/* Show phone input validation errors (only if no Zod errors) */}
              {!errors.telephone && phoneValidation.error && (
                <p className="text-destructive text-sm">
                  {phoneValidation.error}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Controller
                name="terms"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <Checkbox
                    id="terms"
                    checked={value}
                    onCheckedChange={onChange}
                  />
                )}
              />
              <Label htmlFor="terms" className="block text-sm leading-normal">
                By creating an account, I agree to MyKarLink&apos;s{" "}
                <a
                  href="/terms-and-conditions"
                  className="text-primary hover:text-primary/90 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms and Conditions
                </a>
                .
              </Label>
            </div>

            <div className="flex justify-between">
              {showBackButton && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Back
                </Button>
              )}
              <Button
                type="submit"
                disabled={
                  !isValid ||
                  isSubmitting ||
                  isPending ||
                  !phoneValidation.isValid
                }
                className={!showBackButton ? "w-full" : ""}
              >
                {(isSubmitting || isPending) && <Loader />}
                {isSubmitting || isPending ? "Signing up..." : "Sign Up"}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}

export default function SignUpPage() {
  const [userType, setUserType] = useState<UserType>(null);

  const handleUserTypeSelection = (type: UserType) => {
    setUserType(type);
  };

  // Check if this is karlink brand - if not, default to CLIENT
  useEffect(() => {
    if (!isKarlinkBrand()) {
      setUserType("CLIENT");
    }
  }, []);

  // Only show user type selection for karlink brand
  if (!userType && isKarlinkBrand()) {
    return (
      <div className="mx-auto w-full max-w-lg space-y-8">
        <div className="space-y-3 text-center">
          <h1 className="text-3xl font-bold">Sign Up</h1>
          <p className="text-muted-foreground text-lg">
            Choose how you want to use our platform
          </p>
        </div>

        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <Button
              variant="outline"
              onClick={() => handleUserTypeSelection("CLIENT")}
              className="h-auto cursor-pointer flex-col items-center justify-center gap-4 border-2 p-6 text-wrap whitespace-normal"
            >
              <div className="bg-primary/10 rounded-full p-4">
                <Car className="text-primary h-10 w-10" />
              </div>
              <div className="space-y-2 text-center">
                <h3 className="text-xl font-bold">Hire Vehicles</h3>
                <p className="text-sm leading-relaxed">
                  Browse and rent vehicles for your personal or business needs
                </p>
              </div>
            </Button>

            <Button
              variant="outline"
              onClick={() => handleUserTypeSelection("AGENCY")}
              className="h-auto cursor-pointer flex-col items-center justify-center gap-4 border-2 p-6 text-wrap whitespace-normal"
            >
              <div className="bg-secondary/10 rounded-full p-4">
                <Key className="text-secondary h-10 w-10" />
              </div>
              <div className="space-y-2 text-center">
                <h3 className="text-xl font-bold">Provide Vehicles</h3>
                <p className="text-sm leading-relaxed">
                  List your vehicles and earn money by renting them out
                </p>
              </div>
            </Button>
          </div>

          <div className="text-center">
            <Button type="button" variant="link" asChild>
              <Link href="/login">Already have an account? Sign in</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If userType is set or not karlink brand, show the signup form
  if (userType) {
    return <SignupForm userType={userType} onBack={() => setUserType(null)} />;
  }

  // Fallback (should not reach here, but just in case)
  return null;
}
