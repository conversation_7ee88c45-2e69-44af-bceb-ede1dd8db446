import { <PERSON>ada<PERSON> } from "next";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON>er,
	CardTitle,
} from "@/common/components/ui/card";
import {
	HandshakeIcon,
	ShieldCheck,
	Sparkles,
	ClipboardCheck,
	Scale,
	Info,
} from "lucide-react";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { notFound } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
	const brand = getCurrentBrandConfig();

	// Only generate metadata for karlink brand
	if (brand.name !== "karlink") {
		return {
			title: brand.brandName,
		};
	}

	return {
		title: "Community Guidelines - MyKarLink Zimbabwe | Car Sharing Standards",
		description:
			"Read MyKarLink's community guidelines for car sharing in Zimbabwe. Learn about trust, safety, cleanliness standards, and legal requirements for vehicle providers and hirers.",
		keywords: [
			"MyKarLink community guidelines",
			"car sharing rules Zimbabwe",
			"vehicle sharing standards",
			"car rental community policy",
			"MyKarLink terms Zimbabwe",
			"car sharing safety guidelines",
			"vehicle provider responsibilities",
			"car hirer guidelines",
			"car sharing trust standards",
			"Zimbabwe vehicle sharing rules",
			"MyKarLink policies",
			"car rental community standards",
		],
		openGraph: {
			title: "Community Guidelines - MyKarLink Zimbabwe",
			description:
				"Community standards and guidelines for safe, trusted car sharing on MyKarLink's platform in Zimbabwe.",
			type: "article",
			url: "https://mykarlink.com/community-guidelines",
			siteName: "MyKarLink",
			locale: "en_ZW",
			images: [
				{
					url: "https://mykarlink.com/brands/karlink/images/og-image.png",
					width: 1200,
					height: 630,
					alt: "MyKarLink Community Guidelines - Car Sharing Standards",
					type: "image/png",
				},
			],
		},
		twitter: {
			card: "summary_large_image",
			title: "Community Guidelines - MyKarLink Zimbabwe",
			description:
				"Community standards for safe and trusted car sharing in Zimbabwe. Guidelines for vehicle providers and hirers.",
			images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
			creator: "@MyKarLink",
			site: "@MyKarLink",
		},
		alternates: {
			canonical: "https://mykarlink.com/community-guidelines",
		},
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
	};
}

export default function CommunityGuidelinesPage() {
	const brand = getCurrentBrandConfig();

	if (brand.name !== "karlink") {
		notFound();
	}

	const guidelines = [
		{
			icon: HandshakeIcon,
			title: "Do right by each other",
			content:
				"It&apos;s really that simple. In the context of car sharing, this process begins with a vehicle provider offering a car that is ready for the road, and ends with a hirer returning that car in the condition it was originally provided. If something happens along the way, we trust that the responsible party will stand up and resolve the issue.",
		},
		{
			icon: ShieldCheck,
			title: "Inspire trust",
			content:
				"Make sure your profile communicates who you are and includes a clear profile picture and your name. Remember that bookings are for you alone and that if someone else wants to drive your car on MyKarLink, they need to have a MyKarLink - approved account. Describe and photograph your car so that hirers are clear on what you&apos;re offering. Hirers should never tamper with, abuse, or misuse a car booked through the Services.",
		},
		{
			icon: Sparkles,
			title: "Keep it clean",
			content:
				"We don&apos;t just mean the car, but also how you treat each other. It&apos;s okay to disagree, just keep it civil and productive, especially when picking up and dropping off the car. If either you or your host or guest has a concern, discuss it with mutual respect. Most unpleasant conversations can be prevented by keeping the car clean, odor-free, full of fuel, and otherwise road worthy at pickup and return.",
		},
		{
			icon: ClipboardCheck,
			title: "Tidy up loose ends",
			content:
				"While most trips end as soon as vehicle providers get their car and key back, you may still need to square up on tolls, fuel, or even damage after the trip is over. Promptly let the other party know that something&apos;s amiss, and try to resolve the matter quickly. Always use MyKarLink&apos;s Support and advice to help you resolve any issues.",
		},
		{
			icon: Scale,
			title: "Obey the law",
			content:
				"Vehicle providers are responsible for ensuring their vehicle complies with all applicable regulatory laws and hirers are expected to comply with all operating laws.",
		},
		{
			icon: Info,
			title: "And one last thing...",
			content:
				"If you can&apos;t seem to resolve a matter related to any of the above, contact MyKarLink support and we&apos;ll help you resolve it according to our terms of service and policies.",
		},
	];

	// Structured data for Policy/Guidelines page
	const policyJsonLd = {
		"@context": "https://schema.org",
		"@type": "WebPage",
		"@id": "https://mykarlink.com/community-guidelines",
		name: "MyKarLink Community Guidelines",
		description:
			"Community standards and guidelines for safe, trusted car sharing on MyKarLink's platform in Zimbabwe.",
		url: "https://mykarlink.com/community-guidelines",
		mainEntity: {
			"@type": "Article",
			headline: "Community Guidelines for Car Sharing",
			description:
				"Comprehensive guidelines covering trust, safety, cleanliness, and legal compliance for MyKarLink's car sharing community.",
			datePublished: "2024-01-01",
			dateModified: "2024-12-16",
			author: {
				"@type": "Organization",
				name: "MyKarLink",
				url: "https://mykarlink.com",
			},
			publisher: {
				"@type": "Organization",
				name: "MyKarLink",
				logo: {
					"@type": "ImageObject",
					url: "https://mykarlink.com/brands/karlink/images/logo.svg",
				},
			},
		},
		breadcrumb: {
			"@type": "BreadcrumbList",
			itemListElement: [
				{
					"@type": "ListItem",
					position: 1,
					name: "Home",
					item: "https://mykarlink.com",
				},
				{
					"@type": "ListItem",
					position: 2,
					name: "Community Guidelines",
					item: "https://mykarlink.com/community-guidelines",
				},
			],
		},
	};

	// Structured data for Organization policies
	const organizationJsonLd = {
		"@context": "https://schema.org",
		"@type": "Organization",
		"@id": "https://mykarlink.com/#organization",
		name: "MyKarLink",
		url: "https://mykarlink.com",
		logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
		description: "Car rental and vehicle sharing platform in Zimbabwe",
		hasPolicy: [
			{
				"@type": "CreativeWork",
				name: "Community Guidelines",
				url: "https://mykarlink.com/community-guidelines",
				description:
					"Guidelines for maintaining trust, safety, and community standards in car sharing",
				datePublished: "2024-01-01",
				dateModified: "2024-12-16",
			},
		],
		areaServed: {
			"@type": "Country",
			name: "Zimbabwe",
		},
		serviceType: "Car Rental and Vehicle Sharing",
	};

	// Structured data for ItemList of guidelines
	const guidelinesListJsonLd = {
		"@context": "https://schema.org",
		"@type": "ItemList",
		name: "MyKarLink Community Guidelines",
		description: "List of community standards for car sharing platform users",
		numberOfItems: guidelines.length,
		itemListElement: guidelines.map((guideline, index) => ({
			"@type": "ListItem",
			position: index + 1,
			item: {
				"@type": "Thing",
				name: guideline.title,
				description: guideline.content,
			},
		})),
	};

	return (
		<>
			{/* Structured Data */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(policyJsonLd).replace(/</g, "\\u003c"),
				}}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(organizationJsonLd).replace(/</g, "\\u003c"),
				}}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(guidelinesListJsonLd).replace(/</g, "\\u003c"),
				}}
			/>

			<main className="min-h-screen bg-gray-50">
				{/* Header Section */}
				<section className="bg-primary py-16 pt-32 pb-20 text-white">
					<div className="container mx-auto px-4">
						<div className="mx-auto max-w-3xl text-center">
							<h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
								Community Guidelines
							</h1>
							<p className="text-xl text-gray-300">
								Working together to create a trusted car sharing community
							</p>
						</div>
					</div>
				</section>

				{/* Guidelines Content */}
				<section className="py-16">
					<div className="container mx-auto px-4">
						<div className="mx-auto max-w-4xl">
							<div className="mb-8 text-sm text-gray-500">
								Last updated: 16 December 2024
							</div>

							<div className="space-y-6">
								{guidelines.map((guideline, index) => (
									<Card
										key={index}
										className="border-secondary/30 border-l-secondary border-l-4"
									>
										<CardHeader className="flex flex-row items-center gap-4">
											<div className="bg-secondary/10 inline-block rounded-md p-2">
												<guideline.icon className="text-secondary h-6 w-6" />
											</div>
											<CardTitle className="text-primary text-xl font-semibold">
												{guideline.title}
											</CardTitle>
										</CardHeader>
										<CardContent>
											<p className="leading-relaxed text-gray-600">
												{guideline.content}
											</p>
										</CardContent>
									</Card>
								))}
							</div>
						</div>
					</div>
				</section>

				{/* Footer Note */}
				<section className="bg-gray-100 py-16">
					<div className="container mx-auto px-4 text-center">
						<div className="mx-auto max-w-2xl">
							<p className="text-gray-600">
								These guidelines help ensure a positive experience for everyone
								in our community. Thank you for being a part of MyKarLink and
								helping us maintain these standards.
							</p>
						</div>
					</div>
				</section>
			</main>
		</>
	);
}
