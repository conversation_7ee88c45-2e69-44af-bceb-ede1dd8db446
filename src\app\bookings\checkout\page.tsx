"use client";

import Image from "next/image";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardH<PERSON>er,
  CardTitle,
} from "@/common/components/ui/card";
import { Vehicle } from "@/common/models";
import vehiclePlaceholderImg from "@/common/assets/images/placeholder-vehicle-image.webp";
import {
  AlertCircle,
  CreditCard,
  Info,
  Loader2,
  Smartphone,
  Star,
} from "lucide-react";
import { getVehicleFeatures } from "@/common/lib/vehicle-utils";
import { VehicleBooking } from "@/common/models";
import { Alert, AlertDescription } from "@/common/components/ui/alert";
import { Label } from "@/common/components/ui/label";
import { Input } from "@/common/components/ui/input";
import { Button } from "@/common/components/ui/button";
import { Badge } from "@/common/components/ui/badge";
import { Separator } from "@/common/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/common/components/ui/radio-group";
import { Checkbox } from "@/common/components/ui/checkbox";
import { useState, useMemo, useEffect } from "react";
import {
  getAddonIds,
  getBookingItems,
  getBookingTotals,
} from "@/common/lib/booking-utils";
import { Nullish, PaymentMethod } from "@/common/types/utils";
import useCreateBookingQuote from "@/common/hooks/use-create-booking-quote";
import useCreateBooking from "@/common/hooks/use-create-booking";
import { useRouter } from "next/navigation";
import { formatDateWithTime } from "@/common/lib/date-utils";
import Link from "next/link";
import React from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/common/components/ui/table";
import {
  cardPaymentIcons,
  localPaymentIcons,
} from "@/common/config/payment-icons";
import useQuotedBooking from "@/common/hooks/use-quoted-booking";
import useReservedBooking from "@/common/hooks/use-reserved-vehicle-booking";
import { formatCurrency } from "@/common/lib/currency-utils";
import {
  getCurrentBrandConfig,
  isKarlinkBrand,
} from "@/common/config/brands/utils";
import { useCurrencyStore } from "@/common/stores/currency-store";
import { useExchangeRates } from "@/common/hooks/use-exchange-rates";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import { useLocation } from "@/common/hooks/use-location";

export default function CheckoutPage() {
  const { preferredCurrency } = useCurrencyStore();
  const { booking: quotedBooking } = useQuotedBooking();
  const vehicle = quotedBooking?.vehicle;

  console.log("++++ Quoted Booking ++++", quotedBooking);

  // Extract base currency from vehicle
  const baseCurrencies = useMemo(() => {
    if (!vehicle?.agency?.baseCurrency) return [];
    return [vehicle.agency.baseCurrency];
  }, [vehicle?.agency?.baseCurrency]);

  // Fetch exchange rates
  const {
    isLoading: exchangeRatesLoading,
    isError: exchangeRatesError,
    convert,
  } = useExchangeRates({ baseCurrencies });

  // Get base currency
  const baseCurrency = vehicle?.agency?.baseCurrency || "USD";

  if (exchangeRatesLoading) {
    return (
      <section className="container grid gap-8 py-24 lg:py-28">
        <div>
          <h1 className="text-3xl font-bold">Rental Details</h1>
          <p className="text-foreground/80 mt-1">Loading...</p>
        </div>
      </section>
    );
  }

  if (exchangeRatesError) {
    return (
      <section className="container grid gap-8 py-24 lg:py-28">
        <div>
          <h1 className="text-3xl font-bold">Rental Details</h1>
          <p className="mt-1 text-red-600">
            Error loading exchange rates. Please try again.
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="container grid gap-8 py-24 lg:py-28">
      <div>
        <h1 className="text-3xl font-bold">Rental Details</h1>
        <p className="text-foreground/80 mt-1">
          Complete your booking for {vehicle?.name} {vehicle?.model}
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="space-y-8 lg:col-span-2">
          <VehicleDetailsCard
            vehicle={vehicle}
            convertPrice={convert}
            preferredCurrency={preferredCurrency}
            baseCurrency={baseCurrency}
          />
          <BookingDetailsCard
            booking={quotedBooking}
            convertPrice={convert}
            preferredCurrency={preferredCurrency}
            baseCurrency={baseCurrency}
          />
        </div>
        <div className="lg:col-span-1">
          <PaymentDetailsCard
            booking={quotedBooking}
            convertPrice={convert}
            preferredCurrency={preferredCurrency}
            baseCurrency={baseCurrency}
          />
        </div>
      </div>
    </section>
  );
}

function VehicleDetailsCard({
  vehicle,
  convertPrice,
  preferredCurrency,
  baseCurrency,
}: {
  vehicle: Vehicle | null | undefined;
  convertPrice: (amount: number, fromCurrency: string) => number;
  preferredCurrency: string;
  baseCurrency: string;
}) {
  const rate = vehicle?.vehicleRates.find((rate) => rate.weekDay === "MTF");
  const vehicleFeatures = getVehicleFeatures(vehicle);

  // Convert rate to preferred currency
  const convertedRate = rate?.rate ? convertPrice(rate.rate, baseCurrency) : 0;

  return (
    <Card className="p-0">
      <CardContent className="p-5">
        <div className="flex flex-col gap-6 md:flex-row">
          <div className="relative h-80 w-full md:w-1/3 lg:h-auto">
            <Image
              src={vehicle?.mainPhoto ?? vehiclePlaceholderImg}
              alt={`Photo of ${vehicle?.color} ${vehicle?.name} ${vehicle?.model}`}
              fill
              className="rounded-md object-cover"
            />
          </div>
          <div className="flex-1">
            <div className="mb-2 flex flex-col justify-between md:flex-row md:items-center">
              <div>
                <h2 className="text-xl font-bold">
                  {vehicle?.name} {vehicle?.model}
                </h2>
                <p className="text-navy-800 font-medium">
                  {formatCurrency(convertedRate, preferredCurrency)}/Day
                </p>
              </div>
              {vehicle?.rating ? (
                <div className="mt-2 flex items-center md:mt-0">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < Math.floor(vehicle?.rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                    />
                  ))}
                  <span className="ml-2 text-sm">
                    {vehicle.rating.toFixed(2)} Stars
                  </span>
                </div>
              ) : (
                <div className="mt-2 flex items-center md:mt-0">
                  Not yet rated
                </div>
              )}
            </div>

            <div className="mt-4 grid grid-cols-2 gap-3 md:grid-cols-3">
              {vehicleFeatures.map((feature) => (
                <div className="flex items-start gap-2" key={feature.title}>
                  <div className="bg-secondary/15 mt-0.5 rounded-md p-1.5">
                    <feature.icon className="text-secondary" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">{feature.title}</p>
                    <p className="text-sm font-medium">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function BookingDetailsCard({
  booking,
  convertPrice,
  preferredCurrency,
  baseCurrency,
}: {
  booking: Nullish<VehicleBooking>;
  convertPrice: (amount: number, fromCurrency: string) => number;
  preferredCurrency: string;
  baseCurrency: string;
}) {
  const vehicle = booking?.vehicle;
  const client = booking?.client;
  const bookingItems = getBookingItems(booking);
  const bookingTotals = getBookingTotals(booking);
  const query = useVehicleQuery();
  const { data: location } = useLocation(query.state.location);

  // Convert booking totals to preferred currency
  const convertedBookingTotals = bookingTotals
    ? {
        subTotal: convertPrice(bookingTotals.subTotal || 0, baseCurrency),
        reservationFee: convertPrice(
          bookingTotals.reservationFee || 0,
          baseCurrency,
        ),
        discount: convertPrice(bookingTotals.discount || 0, baseCurrency),
        total: convertPrice(bookingTotals.total || 0, baseCurrency),
        payableAtCollection: convertPrice(
          bookingTotals.payableAtCollection || 0,
          baseCurrency,
        ),
        payableOnline: convertPrice(
          bookingTotals.payableOnline || 0,
          baseCurrency,
        ),
        vatAmount: convertPrice(bookingTotals.vatAmount || 0, baseCurrency),
      }
    : null;

  // Convert individual booking items
  const convertedBookingItems = bookingItems.map((item) => ({
    ...item,
    total: convertPrice(item.total, baseCurrency),
  }));

  return (
    <Card className="p-0">
      <CardContent className="p-6">
        <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="rounded-lg border border-blue-100 bg-blue-50 p-3">
            <div className="mb-1 flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <h4 className="font-medium">Pick Up</h4>
            </div>
            <p className="text-sm">
              {formatDateWithTime(booking?.start ?? "")}
            </p>
            <p className="text-sm text-gray-600">
              {location?.city}, {location?.country}
            </p>
          </div>
          <div className="rounded-lg border border-blue-100 bg-blue-50 p-3">
            <div className="mb-1 flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <h4 className="font-medium">Drop Off</h4>
            </div>
            <p className="text-sm">{formatDateWithTime(booking?.end ?? "")}</p>
            <p className="text-sm text-gray-600">
              {location?.city}, {location?.country}
            </p>
          </div>
        </div>

        {/* Customer info */}
        <div className="mb-6">
          <h3 className="mb-3 text-lg font-medium">Customer Information</h3>
          <div className="rounded-lg bg-gray-50 p-4">
            <p className="font-medium">{booking?.firstname}</p>
            <p className="text-gray-600">{booking?.surname}</p>
            <p className="text-gray-600">{client?.telephone}</p>
          </div>
        </div>

        {/* Invoice table */}
        <div className="mb-6 overflow-hidden rounded-lg border">
          <Table>
            <TableHeader className="bg-primary text-white">
              <TableRow>
                <TableHead className="text-left text-white">
                  Description
                </TableHead>
                <TableHead className="text-center text-white">
                  Days Hired
                </TableHead>
                <TableHead className="text-right text-white">
                  Total Amount
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {convertedBookingItems.map((item) => (
                <TableRow key={item.description}>
                  <TableCell>{item.description}</TableCell>
                  <TableCell className="text-center">{item.units}</TableCell>
                  <TableCell className="text-right">
                    {formatCurrency(item.total, preferredCurrency)}
                  </TableCell>
                </TableRow>
              ))}
              <TableRow className="border-b bg-gray-50 font-medium">
                <TableCell colSpan={2}>Subtotal</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.subTotal ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
              <TableRow className="border-b bg-gray-50 font-medium">
                <TableCell colSpan={2}>Reservation Fee</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.reservationFee ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
              {convertedBookingTotals?.discount &&
              convertedBookingTotals.discount > 0 ? (
                <TableRow className="border-b bg-gray-50 font-medium">
                  <TableCell colSpan={2}>Discount</TableCell>
                  <TableCell className="text-right">
                    -
                    {formatCurrency(
                      convertedBookingTotals?.discount ?? 0,
                      preferredCurrency,
                    )}
                  </TableCell>
                </TableRow>
              ) : null}
              <TableRow className="bg-gray-50 font-medium">
                <TableCell colSpan={2}>VAT Total</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.vatAmount ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
              <TableRow className="bg-gray-50 font-medium">
                <TableCell colSpan={2}>Total (Includes VAT Total)</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.total ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Payment breakdown */}
        <div className="mb-6 overflow-hidden rounded-lg border">
          <Table>
            <TableBody>
              <TableRow className="border-b">
                <TableCell className="font-medium">
                  Payable At Collection
                </TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.payableAtCollection ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Payable Online</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(
                    convertedBookingTotals?.payableOnline ?? 0,
                    preferredCurrency,
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Important notes */}
        <div className="space-y-3">
          {vehicle?.excessMileageRate && vehicle.excessMileageRate > 0 ? (
            <Alert
              variant="destructive"
              className="border-red-200 bg-red-50 text-red-800"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="block">
                Note: Exceeding the daily mileage limit of{" "}
                <span className="font-semibold">
                  {vehicle?.maxDailyMileage}KM per day
                </span>{" "}
                will incur an additional charge of{" "}
                <span className="font-semibold">
                  {formatCurrency(
                    convertPrice(vehicle?.excessMileageRate || 0, baseCurrency),
                    preferredCurrency,
                  )}{" "}
                  per KM.
                </span>
              </AlertDescription>
            </Alert>
          ) : null}

          {vehicle?.depositAmt && vehicle.depositAmt > 0 && (
            <Alert className="border-blue-200 bg-blue-50 text-blue-800">
              <Info className="h-4 w-4" />
              <AlertDescription className="block">
                Deposit:{" "}
                <span className="font-semibold">
                  {formatCurrency(
                    convertPrice(vehicle.depositAmt, baseCurrency),
                    preferredCurrency,
                  )}
                </span>{" "}
                cash is required for deposit at vehicle pick up/collection, the
                amount to be returned at vehicle drop off after inspection.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function PaymentDetailsCard({
  booking,
  convertPrice,
  preferredCurrency,
  baseCurrency,
}: {
  booking: Nullish<VehicleBooking>;
  convertPrice: (amount: number, fromCurrency: string) => number;
  preferredCurrency: string;
  baseCurrency: string;
}) {
  const { saveReservedBooking } = useReservedBooking();
  const { saveQuotedBooking } = useQuotedBooking();
  const { state } = useVehicleQuery();
  const router = useRouter();

  const vehicle = booking?.vehicle;
  const invoice = booking?.invoices?.[0];
  const bookingTotals = getBookingTotals(booking);

  // Convert booking totals to preferred currency
  const convertedBookingTotals = bookingTotals
    ? {
        subTotal: convertPrice(bookingTotals.subTotal || 0, baseCurrency),
        reservationFee: convertPrice(
          bookingTotals.reservationFee || 0,
          baseCurrency,
        ),
        discount: convertPrice(bookingTotals.discount || 0, baseCurrency),
        total: convertPrice(bookingTotals.total || 0, baseCurrency),
        payableAtCollection: convertPrice(
          bookingTotals.payableAtCollection || 0,
          baseCurrency,
        ),
        payableOnline: convertPrice(
          bookingTotals.payableOnline || 0,
          baseCurrency,
        ),
        vatAmount: convertPrice(bookingTotals.vatAmount || 0, baseCurrency),
      }
    : null;

  const [promoCode, setPromoCode] = useState<string | null>(null);
  const [appliedPromo, setAppliedPromo] = useState<string | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("STRIPE");
  const [isApplyingPromo, setIsApplyingPromo] = useState(false);
  const [isRemovingPromo, setIsRemovingPromo] = useState(false);

  // Check if both currencies are USD to determine payment method visibility
  const showPaymentMethodSelection =
    preferredCurrency === "USD" && baseCurrency === "USD";

  // Auto-set payment method to STRIPE when currencies are not both USD
  useEffect(() => {
    if (!showPaymentMethodSelection) {
      setPaymentMethod("STRIPE");
    }
  }, [showPaymentMethodSelection]);

  const createBookingQuoteMutation = useCreateBookingQuote();
  const createBookingMutation = useCreateBooking();

  const applyPromoCode = async () => {
    if (!promoCode) return;
    setIsApplyingPromo(true);
    const brandConfig = getCurrentBrandConfig();
    try {
      const data: Partial<
        VehicleBooking & {
          vehicleAddonsIds: number[];
          gatewayType: "STRIPE" | "PAYNOW";
          promoCode: string | null;
          locationId: number | null;
          source: string;
        }
      > = {
        firstname: booking?.firstname,
        surname: booking?.surname,
        email: booking?.email,
        phone: booking?.phone,
        start: booking?.start,
        end: booking?.end,
        vehicleId: booking?.vehicle?.id,
        clientId: booking?.clientId,
        vehicleAddonsIds: getAddonIds(booking),
        gatewayType: paymentMethod,
        promoCode,
        locationId: state.location,
        source: brandConfig.name.toUpperCase(),
      };
      const quotation = await createBookingQuoteMutation.mutateAsync(data);
      if (!createBookingQuoteMutation.error) {
        saveQuotedBooking(quotation);
        setAppliedPromo(promoCode);
      }
    } finally {
      setIsApplyingPromo(false);
    }
  };

  const createBooking = async () => {
    const createdBooking = await createBookingMutation.mutateAsync({
      booking,
      gatewayType: paymentMethod,
      promoCode: appliedPromo,
      locationId: state.location,
      source: isKarlinkBrand() ? "KARLINK" : undefined,
    });

    if (paymentMethod === "STRIPE") {
      saveReservedBooking(createdBooking);
      router.push("/bookings/checkout/stripe");
    } else {
      const invoice = createdBooking.invoices[0];
      router.push(invoice.redirectUrl!);
    }
  };

  const removePromoCode = async () => {
    setIsRemovingPromo(true);
    try {
      const data: Partial<
        VehicleBooking & {
          vehicleAddonsIds: number[];
          gatewayType: "STRIPE" | "PAYNOW";
          locationId: number | null;
          source: string;
        }
      > = {
        firstname: booking?.firstname,
        surname: booking?.surname,
        email: booking?.email,
        phone: booking?.phone,
        start: booking?.start,
        end: booking?.end,
        vehicleId: booking?.vehicle?.id,
        clientId: booking?.clientId,
        vehicleAddonsIds: getAddonIds(booking),
        gatewayType: paymentMethod,
        promoCode: null,
        locationId: state.location,
        source: isKarlinkBrand() ? "KARLINK" : undefined,
      };

      const quotation = await createBookingQuoteMutation.mutateAsync(data);
      if (!createBookingQuoteMutation.error) {
        saveQuotedBooking(quotation);
        setAppliedPromo(null);
        setPromoCode(null);
      }
    } finally {
      setIsRemovingPromo(false);
    }
  };

  return (
    <div className="sticky top-20">
      <Card>
        <CardHeader>
          <CardTitle>Payment Details</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Promo code section */}
          <div className="mb-6 space-y-4">
            <div>
              <Label htmlFor="promoCode" className="mb-2 block">
                Have a promo code?
              </Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    id="promoCode"
                    placeholder="Enter promo code"
                    value={promoCode ?? ""}
                    onChange={(e) => setPromoCode(e.target.value)}
                    className="grow"
                    disabled={isApplyingPromo}
                  />
                  <Button
                    onClick={applyPromoCode}
                    type="button"
                    disabled={isApplyingPromo}
                  >
                    {isApplyingPromo && <Loader2 className="animate-spin" />}
                    {isApplyingPromo ? "Applying..." : "Apply"}
                  </Button>
                </div>
                {createBookingQuoteMutation.error && (
                  <p className="text-sm text-red-600">
                    {createBookingQuoteMutation.error.response?.data.message ||
                      "Invalid promotion code"}
                  </p>
                )}
              </div>
            </div>

            {/* Display applied promotion */}
            {booking?.promotion && (
              <div className="space-y-2">
                <Label className="block">Applied Promotion</Label>
                <div className="flex items-center justify-between rounded-md border border-green-200 bg-green-50 p-3">
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-800 text-green-100 hover:bg-green-800">
                      PROMO
                    </Badge>
                    <span className="font-medium text-green-950">
                      {booking.promotion.code || booking.promotion.title}
                    </span>
                  </div>
                  {booking.promotion.code && (
                    <Button
                      onClick={() => removePromoCode()}
                      variant="ghost"
                      size="sm"
                      className="hover:text-foreground p-2 text-green-900 hover:bg-green-200"
                      disabled={isRemovingPromo}
                    >
                      {isRemovingPromo && <Loader2 className="animate-spin" />}
                      {isRemovingPromo ? "Removing..." : "Remove"}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          <Separator className="my-4" />

          {/* Payment summary */}
          <div className="mb-6 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Subtotal</span>
              <span>
                {formatCurrency(
                  convertedBookingTotals?.subTotal ?? 0,
                  preferredCurrency,
                )}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Reservation Fee</span>
              <span>
                {formatCurrency(
                  convertedBookingTotals?.reservationFee ?? 0,
                  preferredCurrency,
                )}
              </span>
            </div>
            {invoice?.discount && invoice.discount > 0 ? (
              <div className="flex items-center justify-between text-green-600">
                <span>Discount</span>
                <span>
                  -
                  {formatCurrency(
                    convertPrice(invoice.discount, baseCurrency),
                    preferredCurrency,
                  )}
                </span>
              </div>
            ) : null}
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">VAT Total</span>
              <span>
                {formatCurrency(
                  convertedBookingTotals?.vatAmount ?? 0,
                  preferredCurrency,
                )}
              </span>
            </div>
            <Separator />
            <div className="flex items-center justify-between font-bold">
              <span>Total (Includes VAT Total)</span>
              <span>
                {formatCurrency(
                  convertedBookingTotals?.total ?? 0,
                  preferredCurrency,
                )}
              </span>
            </div>
          </div>

          <div className="mb-6 rounded-lg border border-blue-100 bg-blue-50 p-3">
            <p className="text-foreground/80 mb-4 text-sm">
              To secure your booking you will pay the displayed amount online,
              the remainder will be paid in cash at vehicle collection at the
              car rental
            </p>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Payable Online</span>
                <span className="text-lg font-bold text-green-700">
                  {formatCurrency(
                    convertedBookingTotals?.payableOnline ?? 0,
                    preferredCurrency,
                  )}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">
                    Payable at vehicle collection
                  </span>
                </div>
                <span className="font-bold">
                  {formatCurrency(
                    convertedBookingTotals?.payableAtCollection ?? 0,
                    preferredCurrency,
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Payment method */}
          {showPaymentMethodSelection && (
            <div className="w-full">
              <RadioGroup
                value={paymentMethod || ""}
                onValueChange={(value) =>
                  setPaymentMethod(value as PaymentMethod)
                }
              >
                <div className="mb-6 grid grid-cols-1 gap-4">
                  <div>
                    <RadioGroupItem
                      value="STRIPE"
                      id="card"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="card"
                      className="border-muted/60 bg-muted/50 text-primary/50 hover:bg-muted hover:text-primary/70 peer-data-[state=checked]:border-primary/70 [&:has([data-state=checked])]:border-primary/70 flex h-full flex-col items-center justify-between rounded-xl border-2 p-4"
                    >
                      <div className="mb-4 flex w-full items-center justify-center">
                        <CreditCard className="mr-2 h-6 w-6" />
                        <span className="text-lg font-semibold">
                          Debit/Credit & Wallets
                        </span>
                      </div>
                      <div className="flex flex-wrap justify-center space-x-2">
                        {cardPaymentIcons.map((Icon, idx) => (
                          <Icon className="h-auto w-16" key={idx} />
                        ))}
                      </div>
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem
                      value="PAYNOW"
                      id="paynow"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="paynow"
                      className="border-muted/60 bg-muted/50 text-primary/50 hover:bg-muted hover:text-primary/70 peer-data-[state=checked]:border-primary/70 [&:has([data-state=checked])]:border-primary/70 flex h-full flex-col items-center justify-between rounded-xl border-2 p-4"
                    >
                      <div className="mb-4 flex w-full items-center justify-center">
                        <Smartphone className="mr-2 h-6 w-6" />
                        <span className="text-lg font-semibold">
                          Local Payment Options
                        </span>
                      </div>
                      <div className="flex flex-wrap justify-center space-x-2">
                        {localPaymentIcons.map((Icon, idx) => (
                          <Icon className="w-16" key={idx} />
                        ))}
                      </div>
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>
          )}

          {/* Terms and conditions */}
          <div className="mb-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={() => setTermsAccepted(!termsAccepted)}
              />
              <Label htmlFor="terms" className="block text-sm leading-none">
                I accept the{" "}
                <Link
                  href="/terms-and-conditions"
                  className="text-primary hover:underline"
                >
                  terms and conditions
                </Link>{" "}
                in addition to the{" "}
                <span className="text-primary font-semibold">
                  {formatCurrency(
                    convertPrice(vehicle?.depositAmt ?? 0, baseCurrency),
                    preferredCurrency,
                  )}
                </span>{" "}
                deposit for booking this vehicle
              </Label>
            </div>
          </div>

          {/* Pay now button */}
          <Button
            className="w-full"
            size="lg"
            disabled={!termsAccepted || createBookingMutation.isPending}
            onClick={createBooking}
          >
            {createBookingMutation.isPending && (
              <Loader2 className="animate-spin" />
            )}
            Pay{" "}
            {formatCurrency(
              convertedBookingTotals?.payableOnline ?? 0,
              preferredCurrency,
            )}{" "}
            Now
          </Button>

          {createBookingMutation.error && (
            <p className="mt-1 text-sm text-red-600">
              {createBookingMutation.error.message || "An error occurred"}
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
