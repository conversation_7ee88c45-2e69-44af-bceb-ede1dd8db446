"use client";

import { useState } from "react";
import { But<PERSON> } from "@/common/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/common/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { Separator } from "@/common/components/ui/separator";
import { toast } from "sonner";
import useCurrencies from "@/common/hooks/use-currencies";
import { useCurrencyStore } from "@/common/stores/currency-store";
import { Loader2 } from "lucide-react";

export function PreferencesSection() {
  const { data: currencies, isLoading: currenciesLoading } = useCurrencies();
  const { preferredCurrency, setPreferredCurrency } = useCurrencyStore();

  const [selectedCurrency, setSelectedCurrency] = useState(
    preferredCurrency || "USD",
  );
  const [isSaving, setIsSaving] = useState(false);

  const handleCurrencyChange = (currencyCode: string) => {
    setSelectedCurrency(currencyCode);
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);

    try {
      setPreferredCurrency(selectedCurrency);

      toast.success("Preferences updated", {
        description: "Your currency preference has been saved successfully.",
      });
    } catch {
      toast.error("Error", {
        description: "Failed to update preferences. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const selectedCurrencyData = currencies?.find(
    (c) => c.code === selectedCurrency,
  );
  const isChanged = selectedCurrency !== preferredCurrency;

  if (currenciesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <Card className="bg-white">
      <CardHeader>
        <CardTitle className="text-lg">Currency Preferences</CardTitle>
        <p className="text-muted-foreground text-sm">
          Set your preferred currency for viewing prices and making payments
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Currency Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Preferred Currency</label>
          <Select value={selectedCurrency} onValueChange={handleCurrencyChange}>
            <SelectTrigger className="w-full md:w-1/3">
              <SelectValue placeholder="Select a currency" />
            </SelectTrigger>
            <SelectContent>
              {currencies?.map((currency) => (
                <SelectItem key={currency.code} value={currency.code || ""}>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{currency.code}</span>
                    <span className="text-muted-foreground">
                      {currency.name}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Preview Section */}
        {selectedCurrencyData && (
          <div className="space-y-4 rounded-lg border p-4">
            <h4 className="text-base font-medium">Currency Preview</h4>
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">
                  Vehicle rental (per day)
                </span>
                <span className="font-medium">
                  {selectedCurrencyData?.symbol || "$"}50.00{" "}
                  {selectedCurrencyData?.code || selectedCurrency}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">
                  Insurance coverage
                </span>
                <span className="font-medium">
                  {selectedCurrencyData?.symbol || "$"}15.00{" "}
                  {selectedCurrencyData?.code || selectedCurrency}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground text-sm">
                  Total (3 days)
                </span>
                <span className="font-semibold">
                  {selectedCurrencyData?.symbol || "$"}195.00{" "}
                  {selectedCurrencyData?.code || selectedCurrency}
                </span>
              </div>
            </div>

            <Separator />

            <div className="text-muted-foreground text-xs">
              <p className="mb-2">
                Example pricing shown in{" "}
                {selectedCurrencyData?.name || selectedCurrency} (
                {selectedCurrencyData?.code || selectedCurrency})
              </p>
            </div>
          </div>
        )}

        {/* Informational Bullets */}
        <div className="text-muted-foreground text-xs">
          <ul className="list-inside list-disc space-y-1">
            <li>All prices will be displayed in your preferred currency</li>
            <li>Payments will be processed in the selected currency</li>
            <li>Exchange rates are updated in real-time</li>
            <li>You can change this preference at any time</li>
          </ul>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSaveChanges}
            disabled={!isChanged || isSaving}
            className="min-w-[120px]"
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
