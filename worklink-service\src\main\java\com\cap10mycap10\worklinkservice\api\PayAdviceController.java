package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.GeneralResponse;
import com.cap10mycap10.worklinkservice.dto.billing.PayAdviceCreateDto;
import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import com.cap10mycap10.worklinkservice.service.PayAdviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class PayAdviceController {

    private final PayAdviceService payAdviceService;

    public PayAdviceController(PayAdviceService payAdviceService) {
        this.payAdviceService = payAdviceService;
    }


    @PostMapping(value = "payAdvice/worker/create")
    public ResponseEntity<GeneralResponse> createWorkerPayAdvice(HttpServletRequest servletRequest, @RequestParam("workerId") Long workerId,
                                                               @RequestParam("payeeId") Long agentId,
                                                                 @RequestParam("payDate") String payDate,
                                                               @RequestBody PayAdviceCreateDto payAdviceCreateDto
    ) throws Exception {
        log.info("Request to create worker payAdvice : {}", workerId);
        payAdviceService.createPayAdvice(servletRequest, workerId, agentId,payDate , payAdviceCreateDto.getShiftIds());
        return ResponseEntity.ok(new GeneralResponse("PayAdvice created successfully", "00"));
    }


    @GetMapping(value = "payAdvice/worker/view")
    public ResponseEntity<Page<PayAdviceResult>> getWorkerPayAdvices(@RequestParam("workerId") Long workerId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get worker payAdvices : {}", workerId);
        return ResponseEntity.ok(payAdviceService.getAllPayAdvices(workerId, page, size));
    }

    @GetMapping(value = "payAdvice/agency-worker/view")
    public ResponseEntity<Page<PayAdviceResult>> getWorkerPayAdvices(@RequestParam("workerId") Long workerId,
                                                                     @RequestParam("agencyId") Long agencyId,
                                                                     @RequestParam("page") Integer page,
                                                                     @RequestParam("size") Integer size) {
        log.info("Request to get agency-worker payAdvices worker: {}, agency: {}", workerId, agencyId);
        return ResponseEntity.ok(payAdviceService.findAllByWorkerIdAndAgencyId(workerId,agencyId, page, size));
    }

    @GetMapping(value = "payAdvice/agency/view")
    public ResponseEntity<Page<PayAdviceResult>> getAgencyPayAdvices(@RequestParam("agencyId") Long agencyId,
                                                                     @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                     @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                                     @RequestParam(value = "workerId", required = false) Long workerId,
                                                                     @RequestParam("page") Integer page,
                                                                     @RequestParam("size") Integer size) {
        log.info("Request to get worker payAdvices : {}", agencyId);
        return ResponseEntity.ok(payAdviceService.getAllFilteredPayAdvicesForAgent(agencyId,workerId, startDate, endDate, page, size));

    }


    @GetMapping(value = "payAdvice/worker/view/payadvice")
    public ResponseEntity<PayAdviceResult> getWorkerPayAdvice(@RequestParam("payAdviceId") Long payAdviceId) {
        log.info("Request to create worker payAdvice : {}", payAdviceId);
        return ResponseEntity.ok(payAdviceService.findPayAdvice(payAdviceId));
    }


    @PutMapping(value = "payAdvice/agency/acknowledge")
    public ResponseEntity<GeneralResponse> acknowledge(@RequestParam("payAdviceId") Long payAdviceId,
                                                       @RequestParam("paymentRef") String paymentRef) {
        log.info("Request to acknowledge payment for payAdvice : {}", payAdviceId);
        payAdviceService.acknowledgePayment(payAdviceId, paymentRef);
        return ResponseEntity.ok(new GeneralResponse("00", "Payment submitted successfully"));
    }

    @PutMapping(value = "payAdvice/worker/send-pop")
    public ResponseEntity<GeneralResponse> pop(@RequestParam("payAdviceId") Long payAdviceId,
                                               @RequestParam("paymentRef") String paymentRef) {
        log.info("Request to send POP payment for payAdvice : {}", payAdviceId);
        payAdviceService.sendPOP(payAdviceId, paymentRef);
        return ResponseEntity.ok(new GeneralResponse("00", "POP submitted successfully"));
    }

    @GetMapping(value = "payAdvice/download")
    public ResponseEntity<Resource> downloadPayAdvice(@RequestParam Long payAdviceId,
                                                    @RequestParam ReportFormat format,
                                                    HttpServletRequest servletRequest) {

        return payAdviceService.downloadPayAdvice(payAdviceId, format,servletRequest);
    }


    @GetMapping(value = "payadvice/bacs-payment-csv/download/{date}/{agencyId}")
    public ResponseEntity downloadBacsPaymentCsv(@PathVariable("date") String datestr,
                                                           @PathVariable("agencyId") Long agencyId,
                                                           HttpServletRequest servletRequest) {
        try {
            LocalDate date = LocalDate.parse(datestr);

            return payAdviceService.downloadBacsPaymentCsv(date, agencyId, servletRequest);
        }catch (BusinessValidationException e){
            return ResponseEntity.status(404).body(e);
        }
    }



    @PostMapping("/upload-csv-file")
    public ResponseEntity<GeneralResponse> downloadBacsPaymentCsv(@RequestParam("file") MultipartFile file) {

        // validate file
        if (file.isEmpty()) {
            log.info("message", "Please select a CSV file to upload.");
            log.info("status", false);
        } else {
            payAdviceService.uploadBacsPaymentCsv(file);
            return  ResponseEntity.ok(new GeneralResponse("PayAdvices updated successfully", "00"));
        }

        return null;
    }



    @DeleteMapping(value = "pay-advice/{id}")
    public ResponseEntity<Object> delete(@PathVariable("id") Long id) {
        log.info("Request to delete worker pay advice : {}", id);
        payAdviceService.delete(id);
        return ResponseEntity.noContent().build();
    }


}
