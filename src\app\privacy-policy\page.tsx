import { <PERSON>ada<PERSON> } from "next";
import { readFile } from "fs/promises";
import { join } from "path";
import { markdownToHtml } from "@/common/lib/markdown";
import Link from "next/link";

import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/common/components/ui/card";
import { ScrollArea } from "@/common/components/ui/scroll-area";
import { Button } from "@/common/components/ui/button";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { notFound } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
	const brand = getCurrentBrandConfig();

	// Only generate metadata for karlink brand
	if (brand.name !== "karlink") {
		return {
			title: brand.brandName,
		};
	}

	return {
		title:
			"Privacy Policy - MyKarLink Zimbabwe | Data Protection & Privacy Rights",
		description:
			"Read MyKarLink's comprehensive privacy policy for car rental services in Zimbabwe. Learn how we collect, use, and protect your personal information and data privacy rights.",
		keywords: [
			"MyKarLink privacy policy",
			"data protection Zimbabwe",
			"privacy rights car rental",
			"MyKarLink data collection",
			"personal information protection",
			"car rental privacy",
			"Zimbabwe privacy policy",
			"data security car sharing",
			"user privacy rights",
			"MyKarLink data usage",
			"privacy protection Zimbabwe",
			"personal data car rental",
		],
		openGraph: {
			title: "Privacy Policy - MyKarLink Zimbabwe",
			description:
				"Comprehensive privacy policy for car rental services in Zimbabwe. Learn how we protect your personal information and respect your privacy rights.",
			type: "article",
			url: "https://mykarlink.com/privacy-policy",
			siteName: "MyKarLink",
			locale: "en_ZW",
			images: [
				{
					url: "https://mykarlink.com/brands/karlink/images/og-image.png",
					width: 1200,
					height: 630,
					alt: "MyKarLink Privacy Policy - Data Protection & Privacy Rights",
					type: "image/png",
				},
			],
		},
		twitter: {
			card: "summary_large_image",
			title: "Privacy Policy - MyKarLink Zimbabwe",
			description:
				"Comprehensive privacy policy for car rental services. Learn how we protect your personal information and privacy rights.",
			images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
			creator: "@MyKarLink",
			site: "@MyKarLink",
		},
		alternates: {
			canonical: "https://mykarlink.com/privacy-policy",
		},
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
	};
}

export default async function PrivacyPolicyPage() {
	const brand = getCurrentBrandConfig();
	if (brand.name !== "karlink") {
		notFound();
	}

	const markdownPath = join(
		process.cwd(),
		"src/app/privacy-policy/privacy-policy.md",
	);
	const markdown = await readFile(markdownPath, "utf-8");
	const { contentHtml } = await markdownToHtml(markdown);

	// Structured data for Privacy Policy page
	const privacyJsonLd = {
		"@context": "https://schema.org",
		"@type": "WebPage",
		"@id": "https://mykarlink.com/privacy-policy",
		name: "MyKarLink Privacy Policy",
		description:
			"Comprehensive privacy policy outlining how MyKarLink collects, uses, and protects personal information in car rental and vehicle sharing services in Zimbabwe.",
		url: "https://mykarlink.com/privacy-policy",
		mainEntity: {
			"@type": "Article",
			headline: "Privacy Policy",
			description:
				"Legal document describing privacy practices and data protection measures for users of MyKarLink's car rental platform.",
			datePublished: "2024-01-01",
			dateModified: "2024-12-01",
			author: {
				"@type": "Organization",
				name: "MyKarLink",
				url: "https://mykarlink.com",
			},
			publisher: {
				"@type": "Organization",
				name: "MyKarLink",
				logo: {
					"@type": "ImageObject",
					url: "https://mykarlink.com/brands/karlink/images/logo.svg",
				},
			},
			articleSection: "Privacy Policy",
		},
		breadcrumb: {
			"@type": "BreadcrumbList",
			itemListElement: [
				{
					"@type": "ListItem",
					position: 1,
					name: "Home",
					item: "https://mykarlink.com",
				},
				{
					"@type": "ListItem",
					position: 2,
					name: "Privacy Policy",
					item: "https://mykarlink.com/privacy-policy",
				},
			],
		},
	};

	// Structured data for Organization with Privacy Policy
	const organizationJsonLd = {
		"@context": "https://schema.org",
		"@type": "Organization",
		"@id": "https://mykarlink.com/#organization",
		name: "MyKarLink",
		url: "https://mykarlink.com",
		logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
		description: "Car rental and vehicle sharing platform in Zimbabwe",
		privacyPolicy: "https://mykarlink.com/privacy-policy",
		hasPolicy: [
			{
				"@type": "CreativeWork",
				name: "Privacy Policy",
				url: "https://mykarlink.com/privacy-policy",
				description:
					"Policy document outlining data collection, usage, and protection practices",
				datePublished: "2024-01-01",
				dateModified: "2024-12-01",
			},
		],
		areaServed: {
			"@type": "Country",
			name: "Zimbabwe",
		},
		serviceType: "Car Rental and Vehicle Sharing",
		dataPolicy: {
			"@type": "CreativeWork",
			name: "Data Protection Policy",
			description:
				"Comprehensive data protection and privacy policy for user information",
		},
	};

	// Structured data for Privacy Policy as Legal Document
	const legalDocumentJsonLd = {
		"@context": "https://schema.org",
		"@type": "DigitalDocument",
		name: "MyKarLink Privacy Policy",
		description:
			"Legal document outlining data collection practices, privacy rights, and data protection measures for car rental platform users",
		url: "https://mykarlink.com/privacy-policy",
		dateCreated: "2024-01-01",
		dateModified: "2024-12-01",
		creator: {
			"@type": "Organization",
			name: "MyKarLink",
		},
		publisher: {
			"@type": "Organization",
			name: "MyKarLink",
		},
		inLanguage: "en-ZW",
		keywords:
			"privacy policy, data protection, personal information, user privacy, data collection, privacy rights",
		about: {
			"@type": "Service",
			name: "Car Rental Services",
			serviceType: "Vehicle Rental and Sharing",
		},
		hasPart: [
			{
				"@type": "WebPageElement",
				name: "PDF Download",
				url: "https://mykarlink.com/documents/privacy-policy.pdf",
			},
		],
		genre: "Privacy Policy",
	};

	return (
		<main className="min-h-screen bg-gray-50">
			{/* Structured Data */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(privacyJsonLd) }}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(legalDocumentJsonLd),
				}}
			/>
			{/* Header Section */}
			<section className="bg-primary pt-32 pb-20 text-white">
				<div className="container mx-auto px-4">
					<div className="mx-auto max-w-3xl text-center">
						<h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
							Privacy Policy
						</h1>
						<p className="text-xl text-gray-300">
							Your privacy is important to us. Learn how we collect, use, and
							protect your personal information.
						</p>
					</div>
				</div>
			</section>

			{/* Privacy Policy Content */}
			<section className="py-16">
				<div className="container mx-auto px-4">
					<Card className="mx-auto max-w-4xl">
						<CardHeader className="flex items-center justify-between md:flex-row">
							<CardTitle className="text-primary text-2xl font-bold">
								MyKarLink Privacy Policy
							</CardTitle>
							<Link
								href="/documents/privacy-policy.pdf"
								target="_blank"
								download
								className="bg-primary ring-offset-background hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-white transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"
							>
								Download PDF
							</Link>
						</CardHeader>
						<CardContent>
							<ScrollArea className="border-muted h-[60vh] w-full rounded-md border p-4">
								<div className="prose">
									<article>
										<div dangerouslySetInnerHTML={{ __html: contentHtml }} />
									</article>
								</div>
							</ScrollArea>
						</CardContent>
					</Card>
				</div>
			</section>

			{/* Acknowledgment Section */}
			<section className="bg-gray-100 py-16">
				<div className="container mx-auto px-4 text-center">
					<div className="mx-auto max-w-2xl">
						<h2 className="text-primary mb-4 text-2xl font-bold">
							Your Privacy Matters
						</h2>
						<p className="mb-8 text-gray-600">
							By using MyKarLink&apos;s services, you acknowledge that you have
							read and understood our Privacy Policy. We are committed to
							protecting your personal information and respecting your privacy
							rights.
						</p>
						<div className="space-x-4">
							<Button asChild variant="secondary">
								<Link href="/">Continue to Car Search</Link>
							</Button>
							<Button asChild variant="outline">
								<Link href="/contact-us">Contact Us</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
