package com.cap10mycap10.worklinkservice.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;


@Configuration
@EnableSwagger2
public class SwaggerConfig extends WebMvcConfigurationSupport {
    @Value("${env.companyName}")
    private String companyName;
    @Value("${storage.volume.path}")
    private String rootPath;
    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cap10mycap10.worklinkservice"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo());
    }

    private ApiInfo apiInfo() {
        return new ApiInfo(
                companyName+" Service",
                "Clients, Workers, Agencies",
                "1.0",
                "Terms of service",
                new Contact("Tinashe Makarudze", "+263774483751", "<EMAIL>"),
                "License of API", "API license URL", Collections.emptyList());
    }

    @Override
    protected void addResourceHandlers(ResourceHandlerRegistry registry) {
//        String path = "file:///C:/Users/<USER>/Documents/GitHub/worklinkback/worklink-service/src/main/resources";
//        https://qa-api.myworklink.uk/worklink-api/tina/worklink/worker/1/MyWorklinkSlip-1-1-2023-01-17-6058134455873859226hj.jpg



        String path2 = "file:///"+rootPath;

        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");

        registry.addResourceHandler("/tina/**").addResourceLocations(path2);

    }
}
