import { Card, CardContent } from "@/common/components/ui/card";
import { Skeleton } from "@/common/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/common/components/ui/tabs";

export function ProfileSkeleton() {
  return (
    <div className="container pt-28 pb-20">
      <ProfileHeaderSkeleton />
      <ProfileTabsSkeleton />
    </div>
  );
}

export function ProfileHeaderSkeleton() {
  return (
    <Card className="mb-8">
      <CardContent className="flex flex-col items-start gap-6 md:flex-row md:items-center">
        <Skeleton className="h-24 w-24 rounded-full" />

        <div className="w-full flex-1">
          <div className="mb-3 flex flex-col justify-between gap-2 md:flex-row md:items-center">
            <Skeleton className="mb-2 h-8 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>

          {/* Email and Phone */}
          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
            <Skeleton className="h-5 w-48" />
            <Skeleton className="h-5 w-36" />
          </div>

          {/* Other information */}
          <div className="flex flex-wrap items-center gap-4">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-28" />
            <Skeleton className="h-5 w-40" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ProfileTabsSkeleton() {
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between">
        <Tabs defaultValue="bookings" className="w-full">
          <div className="mb-4 flex items-center justify-between">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="bookings">Bookings</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="preferences">Preferences</TabsTrigger>
            </TabsList>

            <Skeleton className="h-10 w-28" />
          </div>

          <TabsContent value="bookings" className="mt-0">
            <BookingsTableSkeleton />
          </TabsContent>

          <TabsContent value="documents" className="mt-0">
            <DocumentsTableSkeleton />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export function BookingsTableSkeleton() {
  return (
    <div className="overflow-hidden rounded-md border">
      <Table>
        <TableHeader className="bg-slate-100">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Reg Number</TableHead>
            <TableHead>Days Hired</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Full Details</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-4 w-6" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-8" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-8 w-8 rounded-full" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex items-center justify-between border-t bg-slate-50 p-4 text-sm">
        <Skeleton className="h-4 w-32" />
        <div className="flex gap-2">
          <Skeleton className="h-9 w-28" />
          <Skeleton className="h-9 w-28" />
        </div>
      </div>
    </div>
  );
}

export function DocumentsTableSkeleton() {
  return (
    <div className="overflow-hidden rounded-md border">
      <Table>
        <TableHeader className="bg-slate-100">
          <TableRow>
            <TableHead>Document</TableHead>
            <TableHead>Upload Date</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 3 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-4 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-8 w-8 rounded-full" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
