import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";
import { z } from "zod";

const resend = new Resend(process.env.RESEND_API_KEY);

// Check if Resend API key is configured
const isResendConfigured = process.env.RESEND_API_KEY &&
  process.env.RESEND_API_KEY !== "re_123456789_placeholder_key_replace_with_real_key";

// Validation schema for contact form
const contactSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the request body
    const validatedData = contactSchema.parse(body);

    const { firstName, lastName, email, message } = validatedData;

    // Check if email service is configured
    if (!isResendConfigured) {
      console.log("Contact form submission (email service not configured):", {
        firstName,
        lastName,
        email,
        message: message.substring(0, 100) + "...",
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        {
          message: "Message received! We'll get back to you soon. (Note: Email service is not configured in development)",
        },
        { status: 200 }
      );
    }
    
    // Create email content
    const emailSubject = `New Contact Form Message from ${firstName} ${lastName}`;
    const emailContent = `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${firstName} ${lastName}</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Message:</strong></p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;">
        ${message.replace(/\n/g, '<br>')}
      </div>
      <hr>
      <p style="color: #666; font-size: 12px;">
        This message was sent from the MyKarLink contact form at ${new Date().toLocaleString()}.
      </p>
    `;

    // Send email to support
    const { data, error } = await resend.emails.send({
      from: "MyKarLink Contact Form <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: emailSubject,
      html: emailContent,
      replyTo: email, // Allow support team to reply directly to the customer
    });

    if (error) {
      console.error("Error sending email:", error);
      return NextResponse.json(
        { error: "Failed to send message. Please try again later." },
        { status: 500 }
      );
    }

    // Send confirmation email to the customer
    const confirmationSubject = "Thank you for contacting MyKarLink";
    const confirmationContent = `
      <h2>Thank you for your message!</h2>
      <p>Dear ${firstName},</p>
      <p>We have received your message and will get back to you as soon as possible.</p>
      <p><strong>Your message:</strong></p>
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0;">
        ${message.replace(/\n/g, '<br>')}
      </div>
      <p>Best regards,<br>
      MyKarLink Team</p>
      <hr>
      <p style="color: #666; font-size: 12px;">
        Email us: <EMAIL><br>
        Visit the MyKarLink website: https://mykarlink.com
      </p>
    `;

    // Send confirmation email (don't fail the main request if this fails)
    try {
      await resend.emails.send({
        from: "MyKarLink <<EMAIL>>",
        to: [email],
        subject: confirmationSubject,
        html: confirmationContent,
      });
    } catch (confirmationError) {
      console.error("Error sending confirmation email:", confirmationError);
      // Don't fail the main request if confirmation email fails
    }

    return NextResponse.json(
      { 
        message: "Message sent successfully! We'll get back to you soon.",
        id: data?.id 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Contact form error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid form data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error. Please try again later." },
      { status: 500 }
    );
  }
}
