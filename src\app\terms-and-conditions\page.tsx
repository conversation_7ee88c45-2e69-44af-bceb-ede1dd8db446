import { <PERSON>ada<PERSON> } from "next";
import { readFile } from "fs/promises";
import { join } from "path";
import { markdownToHtml } from "@/common/lib/markdown";

import Link from "next/link";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/common/components/ui/card";
import { ScrollArea } from "@/common/components/ui/scroll-area";
import { Button } from "@/common/components/ui/button";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { notFound } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
	const brand = getCurrentBrandConfig();

	// Only generate metadata for karlink brand
	if (brand.name !== "karlink") {
		return {
			title: brand.brandName,
		};
	}

	return {
		title:
			"Terms and Conditions - MyKarLink Zimbabwe | Car Rental Terms of Service",
		description:
			"Read MyKarLink's comprehensive terms and conditions for car rental services in Zimbabwe. Understand our terms of service, user agreements, and legal obligations for vehicle sharing.",
		keywords: [
			"MyKarLink terms and conditions",
			"car rental terms of service Zimbabwe",
			"vehicle sharing terms",
			"MyKarLink user agreement",
			"car hire terms Zimbabwe",
			"rental service conditions",
			"Zimbabwe car rental agreement",
			"vehicle rental terms",
			"car sharing legal terms",
			"MyKarLink service agreement",
			"terms of use Zimbabwe",
			"car rental contract terms",
		],
		openGraph: {
			title: "Terms and Conditions - MyKarLink Zimbabwe",
			description:
				"Complete terms and conditions for car rental services in Zimbabwe. Understand our service agreement and legal terms.",
			type: "article",
			url: "https://mykarlink.com/terms-and-conditions",
			siteName: "MyKarLink",
			locale: "en_ZW",
			images: [
				{
					url: "https://mykarlink.com/brands/karlink/images/og-image.png",
					width: 1200,
					height: 630,
					alt: "MyKarLink Terms and Conditions - Car Rental Service Agreement",
					type: "image/png",
				},
			],
		},
		twitter: {
			card: "summary_large_image",
			title: "Terms and Conditions - MyKarLink Zimbabwe",
			description:
				"Comprehensive terms and conditions for car rental services in Zimbabwe. Clear service agreement and legal terms.",
			images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
			creator: "@MyKarLink",
			site: "@MyKarLink",
		},
		alternates: {
			canonical: "https://mykarlink.com/terms-and-conditions",
		},
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
	};
}

export default async function TermsAndConditionsPage() {
	const brand = getCurrentBrandConfig();

	if (brand.name !== "karlink") {
		notFound();
	}

	const markdownPath = join(
		process.cwd(),
		"src/app/terms-and-conditions/terms-of-service.md",
	);
	const markdown = await readFile(markdownPath, "utf-8");
	const { contentHtml } = await markdownToHtml(markdown);

	// Structured data for Terms of Service page
	const termsJsonLd = {
		"@context": "https://schema.org",
		"@type": "WebPage",
		"@id": "https://mykarlink.com/terms-and-conditions",
		name: "MyKarLink Terms and Conditions",
		description:
			"Comprehensive terms and conditions governing the use of MyKarLink's car rental and vehicle sharing services in Zimbabwe.",
		url: "https://mykarlink.com/terms-and-conditions",
		mainEntity: {
			"@type": "Article",
			headline: "Terms and Conditions",
			description:
				"Legal terms governing the relationship between MyKarLink and users of our car rental platform.",
			datePublished: "2024-01-01",
			dateModified: "2024-12-01",
			author: {
				"@type": "Organization",
				name: "MyKarLink",
				url: "https://mykarlink.com",
			},
			publisher: {
				"@type": "Organization",
				name: "MyKarLink",
				logo: {
					"@type": "ImageObject",
					url: "https://mykarlink.com/brands/karlink/images/logo.svg",
				},
			},
			articleSection: "Legal Terms",
		},
		breadcrumb: {
			"@type": "BreadcrumbList",
			itemListElement: [
				{
					"@type": "ListItem",
					position: 1,
					name: "Home",
					item: "https://mykarlink.com",
				},
				{
					"@type": "ListItem",
					position: 2,
					name: "Terms and Conditions",
					item: "https://mykarlink.com/terms-and-conditions",
				},
			],
		},
	};

	// Structured data for Organization with Terms of Service
	const organizationJsonLd = {
		"@context": "https://schema.org",
		"@type": "Organization",
		"@id": "https://mykarlink.com/#organization",
		name: "MyKarLink",
		url: "https://mykarlink.com",
		logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
		description: "Car rental and vehicle sharing platform in Zimbabwe",
		termsOfService: "https://mykarlink.com/terms-and-conditions",
		hasPolicy: [
			{
				"@type": "CreativeWork",
				name: "Terms and Conditions",
				url: "https://mykarlink.com/terms-and-conditions",
				description:
					"Legal terms governing the use of MyKarLink's car rental and vehicle sharing services",
				datePublished: "2024-01-01",
				dateModified: "2024-12-01",
			},
		],
		areaServed: {
			"@type": "Country",
			name: "Zimbabwe",
		},
		serviceType: "Car Rental and Vehicle Sharing",
	};

	// Structured data for Terms of Service as Legal Document
	const legalDocumentJsonLd = {
		"@context": "https://schema.org",
		"@type": "DigitalDocument",
		name: "MyKarLink Terms and Conditions",
		description:
			"Legal document outlining terms of service, user obligations, and service conditions for car rental platform",
		url: "https://mykarlink.com/terms-and-conditions",
		dateCreated: "2024-01-01",
		dateModified: "2024-12-01",
		creator: {
			"@type": "Organization",
			name: "MyKarLink",
		},
		publisher: {
			"@type": "Organization",
			name: "MyKarLink",
		},
		inLanguage: "en-ZW",
		keywords:
			"terms of service, user agreement, car rental terms, legal conditions, service terms",
		about: {
			"@type": "Service",
			name: "Car Rental Services",
			serviceType: "Vehicle Rental and Sharing",
		},
		hasPart: [
			{
				"@type": "WebPageElement",
				name: "PDF Download",
				url: "https://mykarlink.com/documents/terms-of-service.pdf",
			},
		],
		genre: "Legal Document",
	};

	return (
		<main className="min-h-screen bg-gray-50">
			{/* Structured Data */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(termsJsonLd) }}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(legalDocumentJsonLd),
				}}
			/>

			{/* Header Section */}
			<section className="bg-primary pt-32 pb-20 text-white">
				<div className="container mx-auto px-4">
					<div className="mx-auto max-w-3xl text-center">
						<h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
							Terms and Conditions
						</h1>
						<p className="text-xl text-gray-300">
							Please read these terms carefully before using our services
						</p>
					</div>
				</div>
			</section>

			{/* Terms and Conditions Content */}
			<section className="py-16">
				<div className="container mx-auto px-4">
					<Card className="mx-auto max-w-4xl">
						<CardHeader className="flex items-center justify-between md:flex-row">
							<CardTitle className="text-primary text-2xl font-bold">
								MyKarLink Terms of Service
							</CardTitle>
							<Button asChild>
								<Link
									href="/documents/terms-of-service.pdf"
									target="_blank"
									download
								>
									Download PDF
								</Link>
							</Button>
						</CardHeader>
						<CardContent>
							<ScrollArea className="border-muted h-[60vh] w-full rounded-md border p-4">
								<div className="prose">
									<article>
										<div dangerouslySetInnerHTML={{ __html: contentHtml }} />
									</article>
								</div>
							</ScrollArea>
						</CardContent>
					</Card>
				</div>
			</section>

			{/* Acknowledgment Section */}
			<section className="bg-gray-100 py-16">
				<div className="container mx-auto px-4 text-center">
					<div className="mx-auto max-w-2xl">
						<h2 className="text-primary mb-4 text-2xl font-bold">
							Acknowledgment
						</h2>
						<p className="mb-8 text-gray-600">
							By using MyKarLink&apos;s services, you acknowledge that you have
							read, understood, and agree to be bound by these Terms and
							Conditions.
						</p>
						<div className="flex justify-center gap-4">
							<Button asChild variant="secondary">
								<Link href="/">Continue to Car Search</Link>
							</Button>
						</div>
					</div>
				</div>
			</section>
		</main>
	);
}
