"use client";

import { formatCurrency } from "@/common/lib/currency-utils";

export default function DepositBadge({
  depositAmount,
  currency = "USD",
}: {
  depositAmount?: number;
  currency?: string;
}) {
  const hasDeposit = depositAmount && depositAmount > 0;

  const formatPrice = (amount: number) => {
    return formatCurrency(amount, currency);
  };

  return (
    <div className="bg-primary/10 flex items-center gap-1 rounded-md pl-2 text-xs">
      <span className="text-primary font-medium">Deposit</span>
      <div className="bg-primary rounded-md px-2 py-0.5">
        <span className="font-semibold text-white">
          {hasDeposit ? formatPrice(depositAmount) : formatPrice(0)}
        </span>
      </div>
    </div>
  );
}
