package com.cap10mycap10.worklinkservice.config;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;


@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled=true )
@EnableResourceServer
@Slf4j
public class SecurityConfiguration extends ResourceServerConfigurerAdapter {

    private final Environment environment;

    public SecurityConfiguration(final Environment environment) {
        this.environment = environment;
    }

    @Override
    public void configure(final ResourceServerSecurityConfigurer resources) {
        resources
                .resourceId(
                        environment.getRequiredProperty
                                ("security.oauth2.resource.id")
                );
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("*"));
        configuration.setAllowedMethods(List.of("*"));
        configuration.setAllowedHeaders(List.of("*"));
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Override
    @SneakyThrows
    public void configure(final HttpSecurity http) throws Exception {
//        http.authorizeRequests()
//                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
//                .antMatchers("/api/v1/agency/**").authenticated()
//                .antMatchers("/api/v1/client/**").authenticated()
//                .and()
//                .authorizeRequests()
//                .antMatchers("/api/v1/worker/**")
//                .hasRole("ADMIN")
//                .antMatchers("/api/v1/invoice/**")
//                .hasRole("ADMIN")
//                .anyRequest()
//                .permitAll()
//                .and()
//                .httpBasic();

         http.cors().and().csrf().disable().authorizeRequests()
//                .antMatchers("/**").authenticated()
                .antMatchers(HttpMethod.GET,"/agency/**").permitAll()
                .antMatchers(HttpMethod.GET,"/agency/expense-rate/**").authenticated()
                .antMatchers(HttpMethod.GET,"/agency/transporter/**").authenticated()
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                .anyRequest().permitAll();
    }

    @Bean("clientPasswordEncoder")
    PasswordEncoder clientPasswordEncoder() {
        return new BCryptPasswordEncoder(10);
    }
}