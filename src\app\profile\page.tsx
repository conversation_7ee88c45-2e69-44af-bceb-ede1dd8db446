"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/common/components/ui/tabs";
import { Button } from "@/common/components/ui/button";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/common/components/ui/avatar";
import {
  MapPin,
  FileText,
  Star,
  Mail,
  Phone,
  BadgeCheck,
  AlertCircle,
} from "lucide-react";
import { BookingsTable } from "@/app/profile/components/bookings-table";
import { DocumentsTable } from "@/app/profile/components/documents-table";
import { AddDocumentModal } from "@/app/profile/components/add-document-modal";
import { EditProfileDialog } from "@/app/profile/components/edit-profile-dialog";
import { PreferencesSection } from "@/app/profile/components/preferences-section";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { useClient } from "@/common/hooks/use-client";
import { Address, Client } from "@/common/models";
import { cn } from "@/common/lib/shadcn-utils";
import {
  BookingsTableSkeleton,
  ProfileSkeleton,
} from "@/app/profile/components/profile-skeleton";
import useBookings from "@/common/hooks/use-bookings";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Card, CardContent } from "@/common/components/ui/card";
import {
  CreateProviderProfileModal,
  ProviderFormData,
} from "@/app/profile/components/create-provider-profile-modal";
import { PasswordConfirmationModal } from "@/app/profile/components/password-confirmation-modal";
import { ProviderRedirectModal } from "@/app/profile/components/provider-redirect-modal";

export default function UserProfile() {
  const session = useSession();
  const router = useRouter();

  useEffect(() => {
    if (session.status === "unauthenticated") {
      router.push("/login");
    }
  }, [session.status, router]);

  const {
    data: client,
    isLoading,
    error,
  } = useClient(session.data?.user?.clientId);

  if (error) {
    return (
      <div className="container pt-28 pb-20">
        <div className="flex flex-col items-center rounded-lg border border-red-200 bg-red-50 p-8 text-center">
          <AlertCircle className="mb-4 h-12 w-12 text-red-600" />
          <h3 className="mb-2 text-xl font-medium text-red-800">
            Error loading profile
          </h3>
          <p className="mb-6 max-w-md text-red-600">
            {error instanceof Error
              ? error.message
              : "An unknown error occurred"}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <ProfileSkeleton />;
  }

  return (
    <div className="container pt-28 pb-20">
      <ProfileHeader client={client} />
      <ProfileTabs client={client} />
    </div>
  );
}

interface ProfileHeaderProps {
  client: Client | undefined;
}

function ProfileHeader({ client }: ProfileHeaderProps) {
  const session = useSession();
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);
  const [isCreateProviderOpen, setIsCreateProviderOpen] = useState(false);
  const [isPasswordConfirmOpen, setIsPasswordConfirmOpen] = useState(false);
  const [isProviderRedirectOpen, setIsProviderRedirectOpen] = useState(false);
  const [profileData, setProfileData] = useState<ProviderFormData | null>(null);
  const [firstName = "", lastName = ""] = client?.name?.split(" ") ?? [];

  // Check if user is CLIENT type and can create provider profile
  const canCreateProviderProfile = session.data?.user?.userType === "CLIENT";

  const handleProfileDataContinue = (data: ProviderFormData) => {
    setProfileData(data);
    setIsCreateProviderOpen(false);
    setIsPasswordConfirmOpen(true);
  };

  const handlePasswordConfirmBack = () => {
    setIsPasswordConfirmOpen(false);
    setIsCreateProviderOpen(true);
  };

  const handlePasswordConfirmSuccess = () => {
    setIsPasswordConfirmOpen(false);
    setIsProviderRedirectOpen(true);
  };

  return (
    <Card className="mb-6">
      <CardContent className="flex flex-col items-start gap-6 md:flex-row md:items-center">
        <Avatar className="h-24 w-24 border-2 border-gray-100">
          <AvatarImage
            src={client?.logo}
            alt="User avatar"
            className="object-cover"
          />
          <AvatarFallback>
            {firstName[0]}
            {lastName[0]}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1">
          <div className="mb-3 flex flex-col justify-between gap-2 md:flex-row md:items-center">
            <h1 className="text-2xl font-bold">
              {firstName} {lastName}
            </h1>
            <div className="flex flex-col gap-2 md:flex-row">
              {canCreateProviderProfile && (
                <Button
                  variant="default"
                  onClick={() => setIsCreateProviderOpen(true)}
                  className="w-full md:w-auto"
                >
                  Create Car Rental Profile
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => setIsEditProfileOpen(true)}
                className="w-full md:w-auto"
              >
                Edit Profile
              </Button>
            </div>
          </div>

          {/* Email and Phone */}
          <div className="mb-4 flex flex-col gap-4 sm:flex-row">
            <div className="flex items-center text-gray-700">
              <Mail className="mr-2 h-5 w-5 text-gray-500" />
              <span>{client?.email}</span>
            </div>
            <div className="flex items-center text-gray-700">
              <Phone className="mr-2 h-5 w-5 text-gray-500" />
              <span>{client?.telephone}</span>
            </div>
          </div>

          {/* Other information */}
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center text-gray-700">
              <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
              <span className="ml-1">
                {client?.rating ? client.rating.toFixed(1) : "Not rated"}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              <BadgeCheck
                className={cn(
                  "mr-1 h-5 w-5",
                  client?.verified ? "text-blue-500" : "text-gray-400",
                )}
              />
              <span>{client?.verified ? "Verified" : "Unverified"}</span>
            </div>

            <div className="flex items-center text-gray-700">
              <FileText className="mr-1 h-5 w-5" />
              <span>
                {client?.totalBooking}{" "}
                {client?.totalBooking === 1 ? "Booking" : "Bookings"}
              </span>
            </div>

            <div className="flex items-center text-gray-700">
              <MapPin className="mr-1 h-5 w-5" />
              <span>{formatAddress(client?.address) || "Not set"}</span>
            </div>
          </div>
        </div>

        <EditProfileDialog
          open={isEditProfileOpen}
          onOpenChange={setIsEditProfileOpen}
          client={client!}
        />

        {canCreateProviderProfile && (
          <>
            <CreateProviderProfileModal
              open={isCreateProviderOpen}
              onOpenChange={setIsCreateProviderOpen}
              onContinue={handleProfileDataContinue}
              userFirstName={session.data?.user?.firstName || ""}
              userLastName={session.data?.user?.lastName || ""}
              userEmail={session.data?.user?.client?.email || ""}
              userPhone={session.data?.user?.client?.telephone || ""}
            />
            <PasswordConfirmationModal
              open={isPasswordConfirmOpen}
              onOpenChange={setIsPasswordConfirmOpen}
              onSuccess={handlePasswordConfirmSuccess}
              onBack={handlePasswordConfirmBack}
              userEmail={session.data?.user?.client?.email || ""}
              profileData={profileData!}
            />
            <ProviderRedirectModal
              open={isProviderRedirectOpen}
              onOpenChange={setIsProviderRedirectOpen}
            />
          </>
        )}
      </CardContent>
    </Card>
  );
}

interface ProfileTabsProps {
  client: Client | undefined | null;
}

function ProfileTabs({ client }: ProfileTabsProps) {
  const { data, isLoading } = useBookings();
  const [activeTab, setActiveTab] = useState("bookings");
  const [isAddDocumentOpen, setIsAddDocumentOpen] = useState(false);

  return (
    <>
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <Tabs
            defaultValue="bookings"
            className="w-full"
            onValueChange={(value) => setActiveTab(value)}
          >
            <div className="mb-4 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <TabsList className="grid w-full max-w-lg grid-cols-3">
                <TabsTrigger value="bookings">Recent bookings</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="preferences">Preferences</TabsTrigger>
              </TabsList>

              <div className="flex sm:justify-end">
                {activeTab === "bookings" ? (
                  <Button variant="link" asChild>
                    <Link href="/bookings">View all bookings</Link>
                  </Button>
                ) : activeTab === "documents" ? (
                  <Button
                    variant="default"
                    onClick={() => setIsAddDocumentOpen(true)}
                  >
                    Add Document
                  </Button>
                ) : null}
              </div>
            </div>

            <TabsContent value="bookings" className="mt-0">
              {isLoading || !data ? (
                <BookingsTableSkeleton />
              ) : (
                <BookingsTable bookings={data.content} />
              )}
            </TabsContent>

            <TabsContent value="documents" className="mt-0">
              <DocumentsTable client={client!} />
            </TabsContent>

            <TabsContent value="preferences" className="mt-0">
              <PreferencesSection />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <AddDocumentModal
        open={isAddDocumentOpen}
        onOpenChange={setIsAddDocumentOpen}
        client={client!}
      />
    </>
  );
}

function formatAddress(address: Address | null | undefined): string | null {
  if (!address) return null;

  const addressParts = [address.firstLine, address.town, address.county].filter(
    (part) => part,
  ); // Remove null/undefined/empty values

  return addressParts.length > 0 ? addressParts.join(", ") : null;
}
