"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { Menu } from "lucide-react";
import { cn } from "@/common/lib/shadcn-utils";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/common/components/ui/sheet";
import { Button } from "@/common/components/ui/button";
import { UserNav } from "@/app/components/user-nav";
import { useBrandConfig } from "@/common/hooks/use-brand-config";
import UserAuthButtons from "@/app/components/user-auth-buttons";
import CurrencySelector from "@/common/components/currency-selector";

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const session = useSession();
  const brandConfig = useBrandConfig();

  return (
    <nav
      className={cn(
        "fixed top-0 right-0 left-0 z-50 bg-black/10 backdrop-blur-md transition-all duration-300",
      )}
    >
      <div className="container">
        <div className="flex h-20 items-center md:justify-between">
          {/* Logo */}
          <div className="flex flex-1 items-center justify-between md:flex-initial">
            <Link href="/" className="-mt-2 shrink-0">
              <Image
                src={brandConfig.logo.nav}
                alt={`${brandConfig.brandName} logo`}
                className="w-28 object-cover"
                width={80}
                height={26}
              />
            </Link>
            <div className="flex items-center gap-2 md:hidden">
              <div className="mr-2">
                <CurrencySelector />
              </div>
              {session?.status === "authenticated" && <UserNav />}
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden text-white md:flex md:items-center md:space-x-8">
            {brandConfig.links.primary.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className={cn(
                  "hover:text-nav-foreground text-sm transition-all duration-300",
                  pathname === link.href &&
                    "hover:text-nav-foreground/70 text-nav-foreground font-bold",
                )}
              >
                {link.name}
              </Link>
            ))}
          </div>

          {/* User Navigation and Mobile Menu */}
          <div className="hidden items-center space-x-4 md:flex">
            <CurrencySelector />
            {session?.status === "authenticated" ? (
              <UserNav />
            ) : (
              <div className="flex items-center space-x-4">
                <Button variant="outline" asChild>
                  <Link href="/login">Log in</Link>
                </Button>
                <Button asChild variant="secondary">
                  <Link href="/signup">Sign up</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild className="bg-white">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-foreground"
                  aria-label="Open Menu"
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full p-0 sm:w-[400px]">
                <div className="flex h-full flex-col bg-white">
                  <div className="border-muted border-b p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Link
                          href="/"
                          className="block"
                          onClick={() => setIsOpen(false)}
                        >
                          <Image
                            src={
                              brandConfig.logo.navAlt || brandConfig.logo.nav
                            }
                            alt={`${brandConfig.brandName} logo`}
                            className="w-28 object-cover"
                            width={80}
                            height={26}
                          />
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 overflow-auto">
                    <div className="flex flex-col space-y-4 p-4">
                      {brandConfig.links.primary.map((link, index) => (
                        <Link
                          key={index}
                          href={link.href}
                          className={cn(
                            "border-b py-2",
                            pathname === link.href
                              ? "border-secondary text-secondary font-bold"
                              : "border-muted text-secondary/70 hover:text-secondary",
                          )}
                          onClick={() => setIsOpen(false)}
                        >
                          {link.name}
                        </Link>
                      ))}
                    </div>
                  </div>

                  <div className="border-muted flex flex-col gap-4 border-t p-4">
                    <UserAuthButtons onClick={() => setIsOpen(false)} />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
