import { Metadata } from "next";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";

export async function generateMetadata(): Promise<Metadata> {
    const brand = getCurrentBrandConfig();
    // Only generate metadata for karlink brand
    if (brand.name !== "karlink") {
        return {
            title: brand.brandName,
        };
    }

    return {
        title: 'Contact MyKarLink - Get Support for Car Rental in Zimbabwe',
        description: 'Contact MyKarLink for car rental support, booking assistance, and customer service. Reach us via WhatsApp, email, or contact form. We\'re here to help with your vehicle rental needs in Zimbabwe.',
        keywords: [
            'contact MyKarLink',
            'car rental support Zimbabwe',
            'MyKarLink customer service',
            'car rental help',
            'vehicle rental support',
            'Zimbabwe car hire contact',
            'MyKarLink phone number',
            'car rental assistance',
            'booking help Zimbabwe',
            'MyKarLink email',
            'car sharing support',
            'contact car rental company'
        ],

        alternates: {
            canonical: 'https://mykarlink.com/contact-us',
        },

        openGraph: {
            title: 'Contact MyKarLink - Car Rental Support in Zimbabwe',
            description: 'Get in touch with MyKarLink for car rental support and customer service. Available via WhatsApp, email, and contact form.',
            url: 'https://mykarlink.com/contact-us',
            type: 'website',
            siteName: 'MyKarLink',
            locale: 'en_ZW',
            images: [
                {
                    url: 'https://mykarlink.com/brands/karlink/images/og-image.png',
                    width: 1200,
                    height: 630,
                    alt: 'Contact MyKarLink - Customer Support and Service',
                    type: 'image/png',
                },
            ],
        },

        twitter: {
            card: 'summary_large_image',
            title: 'Contact MyKarLink - Car Rental Support in Zimbabwe',
            description: 'Get in touch with MyKarLink for car rental support. Available via WhatsApp, email, and contact form.',
            images: ['https://mykarlink.com/brands/karlink/images/twitter-large.png'],
            creator: '@MyKarLink',
            site: '@MyKarLink',
        },
    }
}
