package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDepositDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.*;

import com.cap10mycap10.worklinkservice.enums.RatingType;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.Rating;
import com.cap10mycap10.worklinkservice.service.VehicleBookingService;
import com.cap10mycap10.worklinkservice.service.VehicleLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.validation.Valid;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.List;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isAdmin;
import static java.util.Objects.isNull;

@Slf4j
@RestController
@Validated
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class VehicleBookingController {
    @Autowired
    private VehicleBookingService vehicleService;

    @Autowired
    private VehicleBookingService vehicleBookingService;
    @Autowired
    private VehicleLogService logService;


    @PostMapping(value = "vehicle-booking", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> create(@Valid @RequestBody VehicleBookingDto vehicle) {
        log.info("Request to create vehicle booking: {}", vehicle);
        if(isNull(vehicle.getLocationId())) throw new BusinessValidationException("Location id is required to place a booking");
        vehicle.setByAgency(false);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.book(vehicle));
    }

    @PostMapping(value = "vehicle-booking/quote", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> getQuote(@Valid @RequestBody VehicleBookingDto vehicle) {
        if(isNull(vehicle.getLocationId())) throw new BusinessValidationException("Location id is required to get a booking quote");
        log.info("Request to get vehicle booking quote : {}", vehicle);
        vehicle.setByAgency(false);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.getBookingQuote(vehicle));
    }

    @PutMapping(value = "vehicle-booking", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> update(@RequestBody VehicleBookingDto vehicle) {
        log.info("Request to update vehicle bookings with : {}", vehicle);

        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.update(vehicle));
    }

    @PostMapping(value = "vehicle-booking/agency", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> createAsAgency(@Valid @RequestBody VehicleBookingDto vehicle) {
        log.info("Request to add vehicle bookings with : {}", vehicle);
        vehicle.setByAgency(true);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.book(vehicle));
    }


    @PutMapping(value = "vehicle-booking/handover", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> handOverVehicle(@Valid @RequestBody VehicleBookingDto vehicle) {
        log.info("Request to handOverVehicle vehicle with : {}", vehicle);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.handOverVehicle(vehicle));
    }


    @PutMapping(value = "vehicle-booking/returnVehicle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDto> returnVehicle(@Valid @RequestBody VehicleBookingDto vehicle) {
        log.info("Request to returnVehicle vehicle with : {}", vehicle);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.returnVehicle(vehicle));
    }



    @PostMapping(value = "vehicle-booking/rating/{vehicleBookingId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Rating> rateVehicleBooking(@RequestBody Rating rating, @PathVariable("vehicleBookingId") Long vehicleBookingId) {
        log.info("Request to rate vehicle with : {}", rating);
        rating.setType(RatingType.VEHICLE);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.rateVehicleBooking(rating, vehicleBookingId));
    }


    @PostMapping(value = "vehicle-booking/client/rating/{vehicleBookingId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Rating> rateClientForBooking(@RequestBody Rating rating, @PathVariable("vehicleBookingId") Long vehicleBookingId) {
        log.info("Request to rate client for booking with : {}", rating);
        rating.setType(RatingType.CLIENT);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleService.rateVehicleBooking(rating, vehicleBookingId));
    }

    @GetMapping(value = "vehicle-booking/{id}")
    public ResponseEntity<VehicleBookingDto> findById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(vehicleService.findById(id));
    }

    @GetMapping(value = "vehicle-booking/email/{id}/{email}")
    public ResponseEntity<VehicleBookingDto> findByIdAndEmail(
            @PathVariable("id") Long id,
            @PathVariable("email") String email
    ) {
        return ResponseEntity.ok(vehicleService.findByIdAndEmail(id, email));
    }

    /**
     * Endpoint to resend a specific email for a booking
     * @param bookingId The ID of the booking
     * @param emailType The type of email to resend
     * @return The updated booking DTO
     */
    @PostMapping(value = "vehicle-booking/resend-email/{bookingId}/{emailType}")
    public ResponseEntity<VehicleBookingDto> resendBookingEmail(
            @PathVariable("bookingId") Long bookingId,
            @PathVariable("emailType") BookingEmailType emailType
    ) {
        log.info("Request to resend {} email for booking ID: {}", emailType, bookingId);
        return ResponseEntity.ok(vehicleBookingService.resendBookingEmail(bookingId, emailType));
    }

    /**
     * Endpoint to cancel a vehicle booking
     * @param bookingId The ID of the booking to cancel
     * @param cancelReason The reason for cancellation (required if within 48 hours of booking start)
     * @param cancelledByAgency Whether the cancellation was initiated by the agency (true) or client (false)
     * @return The updated booking DTO
     */
    @PostMapping(value = "vehicle-booking/cancel/{bookingId}")
    public ResponseEntity<VehicleBookingDto> cancelBooking(
            @PathVariable("bookingId") Long bookingId,
            @RequestParam(value = "reason", required = false) String cancelReason,
            @RequestParam(value = "byAgency", required = true) Boolean cancelledByAgency
    ) {
        log.info("Request to cancel booking ID: {} by {}, reason: {}", bookingId,
                 cancelledByAgency ? "agency" : "client", cancelReason);
        return ResponseEntity.ok(vehicleBookingService.cancelBooking(bookingId, cancelReason, cancelledByAgency));
    }



    @GetMapping(value = "vehicle-booking/paynow-paid/{quoteId}")
    public ResponseEntity<VehicleBookingDto> bookingPaidPaynow(@PathVariable("quoteId") Long quoteId) {
        return ResponseEntity.ok(vehicleBookingService.bookingPaidPaynow(quoteId));
    }
    @PostMapping(value = "vehicle-booking/paynow-paid/{quoteId}")
    public ResponseEntity<VehicleBookingDto> bookingPaidPaynowPost(@PathVariable("quoteId") Long quoteId) {
        return ResponseEntity.ok(vehicleBookingService.bookingPaidPaynow(quoteId));
    }

    /**
     * Endpoint to create a deposit for a vehicle booking
     * @param depositDto The deposit information
     * @return The created deposit DTO
     */
    @PostMapping(value = "vehicle-booking/deposit", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleBookingDepositDto> createDeposit(@Valid @RequestBody VehicleBookingDepositDto depositDto) {
        log.info("Request to create deposit for booking ID: {}", depositDto.getBookingId());
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(vehicleBookingService.createDeposit(depositDto));
    }

    /**
     * Endpoint to get all deposits for a vehicle booking
     * @param bookingId The ID of the booking
     * @return List of deposits for the booking
     */
    @GetMapping(value = "vehicle-booking/{bookingId}/deposits")
    public ResponseEntity<List<VehicleBookingDepositDto>> getDepositsByBookingId(@PathVariable("bookingId") Long bookingId) {
        log.info("Request to get deposits for booking ID: {}", bookingId);
        return ResponseEntity.ok(vehicleBookingService.getDepositsByBookingId(bookingId));
    }
    @PutMapping(value = "vehicle-booking/paynow-paid/{quoteId}")
    public ResponseEntity<VehicleBookingDto> bookingPaidPaynowPut(@PathVariable("quoteId") Long quoteId) {
        return ResponseEntity.ok(vehicleBookingService.bookingPaidPaynow(quoteId));
    }





    @GetMapping(value = "vehicle-booking/vehicle/{id}/{status}/{page}/{size}")
    public ResponseEntity<Page<VehicleLogDto>> findVehicleLogs(@PathVariable("id") Long id,
                                                               @PathVariable("page") int page,
                                                               @PathVariable("size") int size,
                                                               @PathVariable("status") LogStatus status) {
        return ResponseEntity.ok(logService.findForVehicle(id, status, PageRequest.of(page, size)));
    }



    @GetMapping(value = "vehicle-booking/{page}/{size}")
    public ResponseEntity<Page<VehicleBookingDto>> findByAgencyId(@RequestParam(value = "agencyId", required = false) Long agencyId,
                                                                  @PathVariable("page") int page,
                                                                  @PathVariable("size") int size,
                                                                  @RequestParam(value = "statuses", required = false) List<VehicleBookingStatus> statuses,
                                                                  @RequestParam(value = "bookingId", required = false) Long bookingId,
                                                                  @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
                                                                  @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
                                                                  @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                                  @RequestParam(value = "sortBy",  defaultValue = "id") String sortBy,
                                                                  @RequestParam(value = "sortOrder",  defaultValue = "asc") String sortOrder) {

        if(!isAdmin() && agencyId==null) throw new BusinessValidationException("Agency ID is required for non-admin users");

        PageRequest pageRequest = PageRequest.of(page, size,
                sortOrder.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending());
        return ResponseEntity.ok(vehicleBookingService.findAll(agencyId, statuses, searchCriteria, bookingId, start, end, pageRequest));
    }

//    @GetMapping(value = "vehicle-booking/agency/{agencyId}/{page}/{size}")
//    public ResponseEntity<Page<VehicleBookingDto>> findByAgencyId(@PathVariable("agencyId") Long agencyId,
//                                                           @PathVariable("page") int page,
//                                                           @PathVariable("size") int size,
//                                                           @RequestParam(value = "statuses", required = false) List<VehicleBookingStatus> statuses,
//                                                           @RequestParam(value = "bookingId", required = false) Long bookingId,
//                                                           @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
//                                                           @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
//                                                           @RequestParam(value = "searchCriteria", required = false) String searchCriteria) {
//        return ResponseEntity.ok(vehicleBookingService.findByAgency(agencyId, statuses, searchCriteria, bookingId,  start, end, PageRequest.of(page, size)));
//    }

//    @GetMapping(value = "vehicle-booking/{page}/{size}")
//    public ResponseEntity<Page<VehicleBookingDto>> findAll(@RequestParam(value = "agencyId", required = false) Long agencyId,
//                                                                  @PathVariable("page") int page,
//                                                                  @PathVariable("size") int size,
//                                                                  @RequestParam(value = "statuses", required = false) List<VehicleBookingStatus> statuses,
//                                                                  @RequestParam(value = "bookingId", required = false) Long bookingId,
//                                                                  @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime start,
//                                                                  @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime end,
//                                                                  @RequestParam(value = "regNumber", required = false) String regNumber) {
//        return ResponseEntity.ok(vehicleBookingService.findByAgency(agencyId, statuses, regNumber, bookingId,  start, end, PageRequest.of(page, size)));
//    }

    @GetMapping(value = "vehicle-booking/client/{clientId}/{page}/{size}")
    public ResponseEntity<Page<VehicleBookingDto>> findForClient(@PathVariable("clientId") Long clientId,
                                                           @PathVariable("page") int page,
                                                           @PathVariable("size") int size,
                                                           @RequestParam(value="statuses", required = false) List<VehicleBookingStatus> statuses,
                                                           @RequestParam(value="agencyId", required = false) Long agencyId,
                                                           @RequestParam(value = "searchCriteria", required = false) String searchCriteria) {
        return ResponseEntity.ok(vehicleService.findByClient(clientId, statuses,"",agencyId, PageRequest.of(page, size)));
    }







//
//    @PostMapping(value = "stripe/webhook")
//    public ResponseEntity<Object> receiveStripePayment( @RequestBody String payload, @RequestHeader("Stripe-Signature") String sigHeader) throws BadHttpRequest {
//        log.info("Request to register stripe payment : {}", payload);
//        Stripe.apiKey = "sk_test_51Qjd3G2LpuY6cK8k5PcwTf6gXK8ftBjww6WufIwWaG13xaqw3DaQa4K5tslmLzYu4VU1E27abn3Uw2CcgWvqbvH400jlFEunZq";
//        // Replace this endpoint secret with your endpoint's unique secret
//        // If you are testing with the CLI, find the secret by running 'stripe listen'
//        // If you are using an endpoint defined with the API or dashboard, look in your webhook settings
//        // at https://dashboard.stripe.com/webhooks
//
//        Event event = null;
//
//            try {
//                event = ApiResource.GSON.fromJson(payload, Event.class);
//            } catch (JsonSyntaxException e) {
//                // Invalid payload
//                System.out.println("⚠️  Webhook error while parsing basic request.");
//                throw new BadHttpRequest(e);
//            }
//
//        if(endpointSecret != null && sigHeader != null) {
//            // Only verify the event if you have an endpoint secret defined.
//            // Otherwise use the basic event deserialized with GSON.
//            try {
//                event = Webhook.constructEvent(
//                        payload, sigHeader, endpointSecret
//                );
//            } catch (SignatureVerificationException e) {
//                // Invalid signature
//                System.out.println("⚠️  Webhook error while validating signature.");
//                throw new BadHttpRequest(e);
//            }
//        }
//        // Deserialize the nested object inside the event
//        EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
//        StripeObject stripeObject = null;
//        if (dataObjectDeserializer.getObject().isPresent()) {
//            stripeObject = dataObjectDeserializer.getObject().get();
//        } else {
//            // Deserialization failed, probably due to an API version mismatch.
//            // Refer to the Javadoc documentation on `EventDataObjectDeserializer` for
//            // instructions on how to handle this case, or return an error here.
//            throw new RuntimeException("Failed to deserialize");
//        }
//        // Handle the event
//        switch (event.getType()) {
//            case "payment_intent.succeeded":
//                PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
//                System.out.println("Payment for " + paymentIntent.getAmount() + " succeeded.");
//                // Then define and call a method to handle the successful payment intent.
//                // handlePaymentIntentSucceeded(paymentIntent);
//                break;
//            case "payment_method.attached":
//                PaymentMethod paymentMethod = (PaymentMethod) stripeObject;
//                // Then define and call a method to handle the successful attachment of a PaymentMethod.
//                // handlePaymentMethodAttached(paymentMethod);
//                break;
//            default:
//                System.out.println("Unhandled event type: " + event.getType());
//                break;
//        }
//        return ResponseEntity.ok().build();
//
//    }
//
//





}
