import { Metadata } from "next";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/common/components/ui/accordion";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/common/components/ui/card";
import { Al<PERSON><PERSON>ir<PERSON>, ThumbsUp, Clock, Car } from "lucide-react";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { notFound } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
	const brand = getCurrentBrandConfig();

	// Only generate metadata for karlink brand
	if (brand.name !== "karlink") {
		return {
			title: brand.brandName,
		};
	}

	return {
		title:
			"Car Rental Advice for Hirers - MyKarLink Zimbabwe | Rental Tips & FAQ",
		description:
			"Essential advice for car hirers in Zimbabwe. Learn about rental requirements, documents needed, fuel policies, and get answers to frequently asked questions about renting with MyKarLink.",
		keywords: [
			"car rental advice Zimbabwe",
			"car hire tips Zimbabwe",
			"rental car requirements",
			"MyKarLink rental guide",
			"car rental FAQ Zimbabwe",
			"vehicle rental tips",
			"car hire documents needed",
			"rental car insurance Zimbabwe",
			"driving rental car Zimbabwe",
			"car rental best practices",
			"vehicle hire guide Zimbabwe",
			"rental car checklist",
		],
		openGraph: {
			title: "Car Rental Advice for Hirers - MyKarLink Zimbabwe",
			description:
				"Get expert advice on car rentals in Zimbabwe. Learn about requirements, policies, and best practices for a smooth rental experience.",
			type: "article",
			url: "https://mykarlink.com/advice-for-hirers",
			siteName: "MyKarLink",
			locale: "en_ZW",
			images: [
				{
					url: "https://mykarlink.com/brands/karlink/images/og-image.png",
					width: 1200,
					height: 630,
					alt: "Car Rental Advice for Hirers - MyKarLink Zimbabwe",
					type: "image/png",
				},
			],
		},
		twitter: {
			card: "summary_large_image",
			title: "Car Rental Advice for Hirers - MyKarLink Zimbabwe",
			description:
				"Expert car rental tips and FAQ for hirers in Zimbabwe. Everything you need to know for a smooth rental experience.",
			images: ["https://mykarlink.com/brands/karlink/images/twitter-large.png"],
			creator: "@MyKarLink",
			site: "@MyKarLink",
		},
		alternates: {
			canonical: "https://mykarlink.com/advice-for-hirers",
		},
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
	};
}

const faqItems = [
	{
		question: "What documents do I need to rent a car?",
		answer:
			"To rent a car with MyKarLink, you'll need a valid driver's license, a second form of identification (such as a passport or National ID), and a security deposit. International renters may also need to provide an International Driving Permit.",
	},
	{
		question: "Is there a minimum age requirement for renting?",
		answer:
			"Yes, the minimum age to rent a car with MyKarLink is 21 years old. Drivers under 25 may be subject to a young driver surcharge. Some specialty vehicles may have higher age requirements.",
	},
	{
		question: "What's included in the rental price?",
		answer:
			"The standard rental price includes the daily rate, and in cases of limited mileage, an excess mileage fee. Additional services like GPS, child seats, trailers, etc. can be added for an extra fee.",
	},
	{
		question: "How does the fuel policy work?",
		answer:
			"MyKarLink recommends a 'full-to-full' fuel policy. This means if you receive the car with a full tank, you are expected to return it full. If the car isn't returned with fuel as initially received, a refueling fee may be charged.",
	},
	{
		question: "What happens if I return the car late?",
		answer:
			"We allow a 1-hour grace period for returns. After that, late returns may be subject to an additional day's charge based on our terms and conditions, which can be deducted from your deposit. If you know you'll be late, please contact the lessor as soon as possible to discuss options.",
	},
	{
		question: "Can I add an additional driver to my rental?",
		answer:
			"Depending on the lessor, you can add additional drivers to your rental. Each additional driver must meet our age and license requirements and may be subject to extra daily fees.",
	},
	{
		question: "What should I do in case of an accident or breakdown?",
		answer:
			"In case of an accident, ensure everyone's safety first, then contact local authorities if necessary. For any accident or breakdown, call the lessor within 2 hours. MyKarLink's 24/7 support line is available for further support. We'll guide you through the next steps and arrange assistance.",
	},
	{
		question: "Are there any restrictions on where I can drive the rental car?",
		answer:
			"Generally, you can drive anywhere within the region you have rented. However, some vehicles may have restrictions on off-road use. If you plan to cross international borders, please inform the lessor in advance as special permissions may be required.",
	},
];

export default function AdviceForRentersPage() {
	const brand = getCurrentBrandConfig();

	// Only show this page for karlink brand
	if (brand.name !== "karlink") {
		notFound();
	}

	// Structured data for FAQ - only for karlink
	const faqJsonLd = {
		"@context": "https://schema.org",
		"@type": "FAQPage",
		mainEntity: faqItems.map((item) => ({
			"@type": "Question",
			name: item.question,
			acceptedAnswer: {
				"@type": "Answer",
				text: item.answer,
			},
		})),
	};

	const howToJsonLd = {
		"@context": "https://schema.org",
		"@type": "HowTo",
		name: "How to Rent a Car with MyKarLink in Zimbabwe",
		description:
			"Step-by-step guide for renting a car in Zimbabwe with MyKarLink, including requirements, tips, and best practices.",
		image: "https://mykarlink.com/brands/karlink/images/og-image.png",
		totalTime: "PT30M",
		estimatedCost: {
			"@type": "MonetaryAmount",
			currency: "USD",
			value: "25",
		},
		step: [
			{
				"@type": "HowToStep",
				name: "Prepare Required Documents",
				text: "Gather your valid driver's license, second form of identification (passport or National ID), and security deposit.",
			},
			{
				"@type": "HowToStep",
				name: "Read the Rental Agreement",
				text: "Always review the rental agreement carefully before signing. Ask questions if anything is unclear.",
			},
			{
				"@type": "HowToStep",
				name: "Inspect the Vehicle",
				text: "Check the car for existing damage and ensure it's noted before leaving the rental location.",
			},
			{
				"@type": "HowToStep",
				name: "Return on Time and Clean",
				text: "Return the vehicle at the agreed time in good condition and clean to avoid additional charges.",
			},
		],
		supply: [
			{
				"@type": "HowToSupply",
				name: "Valid Driver's License",
			},
			{
				"@type": "HowToSupply",
				name: "Second Form of ID",
			},
			{
				"@type": "HowToSupply",
				name: "Security Deposit",
			},
		],
	};

	// Structured data for Web Page
	const webPageJsonLd = {
		"@context": "https://schema.org",
		"@type": "WebPage",
		"@id": "https://mykarlink.com/advice-for-hirers",
		name: "Car Rental Advice for Hirers in Zimbabwe",
		description:
			"Comprehensive guide providing essential advice for car hirers in Zimbabwe, including rental requirements, tips, and frequently asked questions.",
		url: "https://mykarlink.com/advice-for-hirers",
		inLanguage: "en-ZW",
		isPartOf: {
			"@type": "WebSite",
			"@id": "https://mykarlink.com",
			name: "MyKarLink",
			url: "https://mykarlink.com",
		},
		about: {
			"@type": "Service",
			name: "Car Rental Services",
			provider: {
				"@type": "Organization",
				name: "MyKarLink",
			},
		},
		breadcrumb: {
			"@type": "BreadcrumbList",
			itemListElement: [
				{
					"@type": "ListItem",
					position: 1,
					name: "Home",
					item: "https://mykarlink.com",
				},
				{
					"@type": "ListItem",
					position: 2,
					name: "Advice for Hirers",
					item: "https://mykarlink.com/advice-for-hirers",
				},
			],
		},
		mainEntity: [
			{
				"@type": "FAQPage",
				"@id": "https://mykarlink.com/advice-for-hirers#faq",
			},
			{
				"@type": "HowTo",
				"@id": "https://mykarlink.com/advice-for-hirers#howto",
			},
		],
	};

	return (
		<>
			{/* Structured Data */}
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(faqJsonLd).replace(/</g, "\\u003c"),
				}}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(howToJsonLd).replace(/</g, "\\u003c"),
				}}
			/>
			<script
				type="application/ld+json"
				dangerouslySetInnerHTML={{
					__html: JSON.stringify(webPageJsonLd).replace(/</g, "\\u003c"),
				}}
			/>

			<main className="min-h-screen bg-gray-50">
				{/* Header Section */}
				<section className="bg-primary pt-32 pb-20 text-white">
					<div className="container mx-auto px-4">
						<div className="mx-auto max-w-3xl text-center">
							<h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
								Advice for Hirers
							</h1>
							<p className="text-xl text-gray-300">
								Everything you need to know for a smooth car rental experience
								with MyKarLink
							</p>
						</div>
					</div>
				</section>

				{/* Quick Tips Section */}
				<section className="py-16">
					<div className="container mx-auto px-4">
						<h2 className="text-primary mb-8 text-center text-2xl font-bold">
							Quick Rental Tips
						</h2>
						<div className="grid gap-8 md:grid-cols-3">
							{[
								{
									icon: AlertCircle,
									title: "Read the Agreement",
									description:
										"Always review the rental agreement carefully before signing. Ask questions if anything is unclear.",
								},
								{
									icon: ThumbsUp,
									title: "Inspect the Vehicle",
									description:
										"Check the car for existing damage and ensure it's noted before leaving the rental location.",
								},
								{
									icon: Clock,
									title: "Returning the vehicle",
									description:
										"Return the vehicle at the agreed time to avoid additional charges or inconveniences. Return the vehicle in a good condition and clean as you may have found it",
								},
							].map((tip, index) => (
								<Card key={index}>
									<CardHeader>
										<div>
											<div className="bg-secondary/10 mb-2 inline-block rounded-full p-2">
												<tip.icon className="text-secondary h-6 w-6" />
											</div>
										</div>
										<CardTitle>{tip.title}</CardTitle>
									</CardHeader>
									<CardContent>
										<CardDescription>{tip.description}</CardDescription>
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</section>

				{/* FAQ Section */}
				<section className="bg-white py-16">
					<div className="container mx-auto px-4">
						<div className="mx-auto max-w-3xl">
							<h2 className="text-primary mb-8 text-center text-2xl font-bold">
								Frequently Asked Questions
							</h2>
							<Accordion type="single" collapsible className="w-full">
								{faqItems.map((item, index) => (
									<AccordionItem
										value={`item-${index}`}
										key={index}
										className="border-b-muted"
									>
										<AccordionTrigger>{item.question}</AccordionTrigger>
										<AccordionContent>{item.answer}</AccordionContent>
									</AccordionItem>
								))}
							</Accordion>
						</div>
					</div>
				</section>

				{/* CTA Section */}
				<section className="bg-gray-100 py-16">
					<div className="container mx-auto px-4 text-center">
						<div className="mx-auto max-w-2xl">
							<h2 className="text-primary mb-4 text-2xl font-bold">
								Ready to Hit the Road?
							</h2>
							<p className="mb-8 text-gray-600">
								With MyKarLink, you&apos;re always in good hands. Our team is
								here to ensure your rental experience is smooth and enjoyable.
							</p>
							<div className="bg-secondary/10 mb-4 inline-block rounded-full p-3">
								<Car className="text-secondary h-8 w-8" />
							</div>
							<p className="text-primary font-semibold">
								Book your car today and experience the MyKarLink difference!
							</p>
						</div>
					</div>
				</section>
			</main>
		</>
	);
}
