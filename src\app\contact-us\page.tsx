"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/common/components/ui/button";
import { Input } from "@/common/components/ui/input";
import { Textarea } from "@/common/components/ui/textarea";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/common/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/common/components/ui/form";
import { Mail, MessageSquare } from "lucide-react";
import { WhatsAppIcon } from "@/common/components/whatsapp-icon";
import { toast } from "sonner";

const formSchema = z.object({
  firstName: z.string().min(2, {
    message: "First name must be at least 2 characters.",
  }),
  lastName: z.string().min(2, {
    message: "Last name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  message: z.string().min(10, {
    message: "Message must be at least 10 characters.",
  }),
});

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Structured data for Contact Us page
  const contactPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    name: "Contact MyKarLink",
    description:
      "Get in touch with MyKarLink for car rental support, booking assistance, and customer service in Zimbabwe.",
    url: "https://mykarlink.com/contact-us",
    mainEntity: {
      "@type": "Organization",
      name: "MyKarLink",
      contactPoint: [
        {
          "@type": "ContactPoint",
          telephone: "+263779144386",
          contactType: "customer service",
          availableLanguage: ["English"],
          areaServed: "ZW",
          hoursAvailable: {
            "@type": "OpeningHoursSpecification",
            dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            opens: "09:00",
            closes: "17:00",
          },
        },
        {
          "@type": "ContactPoint",
          email: "<EMAIL>",
          contactType: "customer service",
          availableLanguage: ["English"],
          areaServed: "ZW",
        },
      ],
      address: {
        "@type": "PostalAddress",
        streetAddress: "7 St Antony, Cnr Five Av and Fifth St",
        addressLocality: "Harare",
        addressCountry: "ZW",
      },
    },
    breadcrumb: {
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: "https://mykarlink.com",
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Contact Us",
          item: "https://mykarlink.com/contact-us",
        },
      ],
    },
  };

  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "MyKarLink",
    legalName: "MyKarLink PVT LTD",
    url: "https://mykarlink.com",
    logo: "https://mykarlink.com/brands/karlink/images/logo.svg",
    description: "Leading car rental and vehicle sharing platform in Zimbabwe",
    contactPoint: [
      {
        "@type": "ContactPoint",
        telephone: "+263779144386",
        contactType: "customer service",
        email: "<EMAIL>",
        availableLanguage: ["English"],
        areaServed: "ZW",
        hoursAvailable: {
          "@type": "OpeningHoursSpecification",
          dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
          opens: "09:00",
          closes: "17:00",
        },
      },
      {
        "@type": "ContactPoint",
        url: "https://wa.me/263779144386",
        contactType: "customer service",
        availableLanguage: ["English"],
        areaServed: "ZW",
        name: "WhatsApp Support",
      },
    ],
    address: {
      "@type": "PostalAddress",
      streetAddress: "7 St Antony, Cnr Five Av and Fifth St",
      addressLocality: "Harare",
      addressCountry: "ZW",
    },
    sameAs: [
      "https://www.facebook.com/people/My-Karlink/61572568015730/",
      "https://www.instagram.com/my_karlink/",
      "https://x.com/My_KarLink",
      "https://www.youtube.com/@mykarlink",
      "https://www.tiktok.com/@mykarlink",
    ],
    serviceType: "Car Rental and Vehicle Sharing",
    areaServed: {
      "@type": "Country",
      name: "Zimbabwe",
    },
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      message: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send message");
      }

      toast("Message Sent", {
        description: data.message || "We've received your message and will get back to you soon.",
      });
      form.reset();
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message", {
        description: error instanceof Error ? error.message : "Please try again later.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(contactPageJsonLd).replace(/</g, "\\u003c"),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationJsonLd).replace(/</g, "\\u003c"),
        }}
      />

      <main className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <section className="bg-primary pt-32 pb-20 text-white">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-3xl text-center">
              <h1 className="mb-4 text-3xl font-bold tracking-tight sm:text-5xl">
                Get in Touch
              </h1>
              <p className="text-xl text-gray-300">
                We&apos;re here to help and answer any question you might have.
                We look forward to hearing from you.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information and Form Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="mb-12 grid gap-8 md:grid-cols-2">
                {[
                  {
                    icon: WhatsAppIcon,
                    title: "WhatsApp Us",
                    content: "+263779144386",
                    href: "https://wa.me/263779144386",
                  },
                  {
                    icon: Mail,
                    title: "Email Us",
                    content: "<EMAIL>",
                    href: "mailto:<EMAIL>",
                  },
                ].map((item, index) => (
                  <a href={item.href} key={index} target="_blank">
                    <Card className="text-center">
                      <CardContent>
                        <div className="bg-secondary/15 mb-4 inline-block rounded-full p-3">
                          <item.icon className="text-secondary h-6 w-6" />
                        </div>
                        <CardTitle className="mb-2 text-lg">
                          {item.title}
                        </CardTitle>
                        <CardDescription>{item.content}</CardDescription>
                      </CardContent>
                    </Card>
                  </a>
                ))}
              </div>

              <Card>
                <CardHeader className="text-center">
                  <div>
                    <div className="bg-secondary/10 mb-4 inline-block rounded-full p-3">
                      <MessageSquare className="text-secondary h-6 w-6" />
                    </div>
                  </div>
                  <CardTitle className="text-primary text-2xl font-bold">
                    Send Us a Message
                  </CardTitle>
                  <CardDescription>
                    Fill out the form below and we&apos;ll get back to you as
                    soon as possible.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-6"
                    >
                      <div className="grid gap-6 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                <Input placeholder="John" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Doe" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="message"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Message</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="How can we help you?"
                                className="min-h-[120px]"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button
                        type="submit"
                        className="w-full"
                        variant="secondary"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? "Sending..." : "Send Message"}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}
